#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSort集成脚本
基于YOLOv5 + DeepSort的目标检测与跟踪
"""

import os
import sys
import cv2
import numpy as np
import json
import csv
from pathlib import Path
from datetime import datetime
import time
import copy

# 添加项目路径
current_dir = Path(__file__).parent
project_dir = current_dir.parent
yolo_dir = project_dir / "相关源码和模型" / "VehicleCountor" / "yolov5"
tracker_dir = project_dir / "相关源码和模型" / "VehicleCountor" / "tracker"

# 添加路径到系统路径
if str(yolo_dir) not in sys.path:
    sys.path.append(str(yolo_dir))
if str(tracker_dir) not in sys.path:
    sys.path.append(str(tracker_dir))

from models.common import DetectMultiBackend
from utils.general import (LOGGER, colorstr, cv2, non_max_suppression, 
                          scale_coords, xyxy2xywh)
from utils.plots import Annotator, colors
from utils.torch_utils import select_device, time_sync

# 直接导入DeepSort
import sys
sys.path.append(str(tracker_dir))
from deep_sort import DeepSort


class DeepSortProcessor:
    def __init__(self, yolo_weights, deepsort_model, device='', conf_thres=0.25, iou_thres=0.45, max_det=1000):
        """初始化DeepSort处理器"""
        self.device = select_device(device)
        
        # 加载YOLOv5模型
        self.model = DetectMultiBackend(yolo_weights, device=self.device)
        self.stride, self.names, self.pt = self.model.stride, self.model.names, self.model.pt
        
        # 加载DeepSort模型
        self.deepsort = DeepSort(
            deepsort_model,
            self.device,
            max_dist=0.2,
            max_iou_distance=0.7,
            max_age=70,
            n_init=3,
            nn_budget=100
        )
        
        self.conf_thres = conf_thres
        self.iou_thres = iou_thres
        self.max_det = max_det
        
        # 统计信息
        self.frame_count = 0
        self.total_objects = 0
        self.class_counts = {name: 0 for name in self.names}
        self.track_history = {}  # 轨迹历史
        self.frame_statistics = []
        
        print(f"✅ YOLOv5模型加载完成: {yolo_weights}")
        print(f"✅ DeepSort模型加载完成: {deepsort_model}")
        print(f"✅ 设备: {self.device}")
        print(f"✅ 类别: {self.names}")
        
    def process_video_with_tracking(self, video_path, output_path=None, show_video=True, save_video=True):
        """处理视频并进行目标跟踪"""
        print(f"🎬 开始DeepSort视频处理: {video_path}")
        
        # 打开视频文件
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            print(f"❌ 无法打开视频文件: {video_path}")
            return None
        
        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"📹 视频信息:")
        print(f"  FPS: {fps}")
        print(f"  分辨率: {width}x{height}")
        print(f"  总帧数: {total_frames}")
        print(f"  时长: {total_frames/fps:.2f}秒")
        
        # 设置输出视频
        video_writer = None
        if save_video and output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
            print(f"💾 输出视频: {output_path}")
        
        # 模型预热
        self.model.warmup(imgsz=(1, 3, 640, 640))
        
        # 处理每一帧
        start_time = time.time()
        frame_times = []
        
        print(f"\n🚀 开始DeepSort处理...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            self.frame_count += 1
            
            # 处理当前帧
            t1 = time_sync()
            processed_frame, frame_stats = self.process_frame_with_tracking(frame)
            t2 = time_sync()
            frame_time = t2 - t1
            frame_times.append(frame_time)
            
            # 显示进度
            if self.frame_count % 100 == 0:
                progress = (self.frame_count / total_frames) * 100
                elapsed_time = time.time() - start_time
                avg_fps = self.frame_count / elapsed_time
                remaining_time = (total_frames - self.frame_count) / avg_fps if avg_fps > 0 else 0
                print(f"📊 进度: {progress:.1f}% ({self.frame_count}/{total_frames}) - FPS: {avg_fps:.1f} - 剩余时间: {remaining_time:.1f}秒")
            
            # 显示视频
            if show_video:
                # 缩放显示（如果分辨率太高）
                display_frame = processed_frame
                if width > 1920 or height > 1080:
                    scale = min(1920/width, 1080/height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    display_frame = cv2.resize(processed_frame, (new_width, new_height))
                
                cv2.imshow('YOLOv5 + DeepSort Tracking', display_frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            # 保存视频
            if video_writer:
                video_writer.write(processed_frame)
            
            # 保存帧统计
            frame_stats['frame_number'] = self.frame_count
            frame_stats['processing_time'] = frame_time
            self.frame_statistics.append(frame_stats)
        
        # 清理资源
        cap.release()
        if video_writer:
            video_writer.release()
        if show_video:
            cv2.destroyAllWindows()
        
        # 计算处理统计
        total_time = time.time() - start_time
        avg_fps = self.frame_count / total_time
        avg_frame_time = np.mean(frame_times)
        
        print(f"\n🎉 DeepSort视频处理完成!")
        print(f"📊 处理统计:")
        print(f"  总帧数: {self.frame_count}")
        print(f"  总目标数: {self.total_objects}")
        print(f"  处理时间: {total_time:.2f}秒")
        print(f"  平均FPS: {avg_fps:.1f}")
        print(f"  平均帧处理时间: {avg_frame_time*1000:.1f}ms")
        
        return {
            'total_frames': self.frame_count,
            'total_objects': self.total_objects,
            'class_counts': self.class_counts,
            'track_history': self.track_history,
            'processing_time': total_time,
            'avg_fps': avg_fps,
            'frame_statistics': self.frame_statistics
        }
    
    def process_frame_with_tracking(self, frame):
        """处理单帧并进行目标跟踪"""
        # 预处理
        im = cv2.resize(frame, (640, 640))
        im = im.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
        im = np.ascontiguousarray(im)
        im = np.expand_dims(im, axis=0)
        
        # 转换为tensor
        import torch
        im = torch.from_numpy(im).to(self.device)
        im = im.half() if self.model.fp16 else im.float()
        im /= 255.0
        
        # YOLOv5推理
        pred = self.model(im, augment=False, visualize=False)
        
        # NMS
        pred = non_max_suppression(pred, self.conf_thres, self.iou_thres, 
                                 classes=None, max_det=self.max_det)
        
        # 处理检测结果
        frame_stats = {
            'objects_detected': 0,
            'tracks_active': 0,
            'class_detections': {name: 0 for name in self.names}
        }
        
        annotator = Annotator(frame, line_width=3, example=str(self.names))
        
        for det in pred:
            if len(det):
                # 缩放边界框到原始尺寸
                det[:, :4] = scale_coords(im.shape[2:], det[:, :4], frame.shape).round()
                
                # 准备DeepSort输入
                xywhs = xyxy2xywh(det[:, 0:4])
                confs = det[:, 4]
                clss = det[:, 5]
                
                # DeepSort跟踪
                outputs = self.deepsort.update(xywhs.cpu(), confs.cpu(), clss.cpu(), frame)
                
                # 处理跟踪结果
                if len(outputs) > 0:
                    frame_stats['tracks_active'] = len(outputs)
                    
                    for output in outputs:
                        x1, y1, x2, y2, track_id, class_id = output
                        
                        # 更新轨迹历史
                        if track_id not in self.track_history:
                            self.track_history[track_id] = []
                        
                        center_x = (x1 + x2) // 2
                        center_y = (y1 + y2) // 2
                        self.track_history[track_id].append({
                            'frame': self.frame_count,
                            'x': center_x,
                            'y': center_y,
                            'class': int(class_id)
                        })
                        
                        # 统计检测结果
                        class_name = self.names[int(class_id)]
                        frame_stats['class_detections'][class_name] += 1
                        frame_stats['objects_detected'] += 1
                        self.class_counts[class_name] += 1
                        self.total_objects += 1
                        
                        # 绘制边界框和轨迹
                        label = f'{class_name} {track_id}'
                        color = colors(track_id % 20, True)  # 使用track_id作为颜色
                        annotator.box_label([x1, y1, x2, y2], label, color=color)
                        
                        # 绘制轨迹
                        if len(self.track_history[track_id]) > 1:
                            points = []
                            for point in self.track_history[track_id][-10:]:  # 最近10个点
                                points.append((point['x'], point['y']))
                            
                            if len(points) > 1:
                                points = np.array(points, dtype=np.int32)
                                cv2.polylines(frame, [points], False, color, 2)
        
        # 在帧上显示统计信息
        self.draw_tracking_statistics(frame, frame_stats)
        
        return frame, frame_stats
    
    def draw_tracking_statistics(self, frame, frame_stats):
        """在帧上绘制跟踪统计信息"""
        # 绘制当前帧统计
        y_offset = 30
        cv2.putText(frame, f"Frame: {self.frame_count}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        y_offset += 25
        
        cv2.putText(frame, f"Objects: {frame_stats['objects_detected']}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        y_offset += 20
        
        cv2.putText(frame, f"Tracks: {frame_stats['tracks_active']}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        y_offset += 20
        
        # 绘制类别统计
        for class_name, count in frame_stats['class_detections'].items():
            if count > 0:
                cv2.putText(frame, f"{class_name}: {count}", (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                y_offset += 18
        
        # 绘制总统计
        y_offset += 10
        cv2.putText(frame, f"Total: {self.total_objects}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        y_offset += 25
        
        for class_name, count in self.class_counts.items():
            if count > 0:
                cv2.putText(frame, f"{class_name}: {count}", (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                y_offset += 18
    
    def save_tracking_statistics(self, stats, output_dir):
        """保存跟踪统计结果"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存JSON统计
        json_path = output_dir / "deepsort_statistics.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        # 保存轨迹数据
        tracks_path = output_dir / "track_history.json"
        with open(tracks_path, 'w', encoding='utf-8') as f:
            json.dump(self.track_history, f, indent=2, ensure_ascii=False)
        
        # 保存CSV帧级统计
        csv_path = output_dir / "deepsort_frame_statistics.csv"
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            if stats['frame_statistics']:
                fieldnames = ['frame_number', 'objects_detected', 'tracks_active', 'processing_time'] + list(self.names)
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for frame_stat in stats['frame_statistics']:
                    row = {
                        'frame_number': frame_stat['frame_number'],
                        'objects_detected': frame_stat['objects_detected'],
                        'tracks_active': frame_stat['tracks_active'],
                        'processing_time': frame_stat['processing_time']
                    }
                    row.update(frame_stat['class_detections'])
                    writer.writerow(row)
        
        # 保存文本摘要
        txt_path = output_dir / "deepsort_summary.txt"
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("=== YOLOv5 + DeepSort跟踪统计摘要 ===\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总帧数: {stats['total_frames']}\n")
            f.write(f"总目标数: {stats['total_objects']}\n")
            f.write(f"处理时间: {stats['processing_time']:.2f}秒\n")
            f.write(f"平均FPS: {stats['avg_fps']:.1f}\n")
            f.write(f"平均每帧目标数: {stats['total_objects']/stats['total_frames']:.2f}\n")
            f.write(f"轨迹数量: {len(self.track_history)}\n\n")
            
            f.write("各类别统计:\n")
            for class_name, count in stats['class_counts'].items():
                if count > 0:
                    percentage = (count / stats['total_objects']) * 100 if stats['total_objects'] > 0 else 0
                    f.write(f"  {class_name}: {count} ({percentage:.1f}%)\n")
        
        print(f"📄 DeepSort统计报告已保存到: {output_dir}")
        return json_path, csv_path, txt_path, tracks_path


def main():
    """主函数"""
    print("🎬 YOLOv5 + DeepSort集成系统")
    print("=" * 60)
    
    # 配置参数
    yolo_weights = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    deepsort_model = tracker_dir / "deep" / "checkpoint" / "osnet_ibn_x1_0_MSMT17.pth"
    video_path = project_dir / "课后作业视频" / "3007580-uhd_3840_2160_30fps.mp4"
    
    # 检查文件是否存在
    if not yolo_weights.exists():
        print(f"❌ YOLOv5模型文件不存在: {yolo_weights}")
        return
    
    if not deepsort_model.exists():
        print(f"❌ DeepSort模型文件不存在: {deepsort_model}")
        return
    
    if not video_path.exists():
        print(f"❌ 视频文件不存在: {video_path}")
        return
    
    print(f"✅ YOLOv5模型: {yolo_weights}")
    print(f"✅ DeepSort模型: {deepsort_model}")
    print(f"✅ 视频文件: {video_path}")
    print()
    
    # 设置输出路径
    output_video = video_path.parent / f"{video_path.stem}_deepsort{video_path.suffix}"
    
    # 创建处理器
    processor = DeepSortProcessor(
        yolo_weights=str(yolo_weights),
        deepsort_model=str(deepsort_model),
        device='',  # 自动选择设备
        conf_thres=0.25,
        iou_thres=0.45,
        max_det=1000
    )
    
    print("开始DeepSort处理...")
    print("-" * 40)
    
    # 处理视频
    stats = processor.process_video_with_tracking(
        video_path=video_path,
        output_path=output_video,
        show_video=True,  # 显示处理过程
        save_video=True   # 保存处理结果
    )
    
    if stats:
        # 保存统计报告
        output_dir = video_path.parent / "deepsort_reports"
        processor.save_tracking_statistics(stats, output_dir)
        
        print("\n" + "=" * 60)
        print("🎉 DeepSort处理完成！")
        print("📁 输出文件:")
        print(f"  视频: {output_video}")
        print(f"  报告: {output_dir}")
        print("\n📊 处理统计:")
        print(f"  总帧数: {stats['total_frames']}")
        print(f"  总目标数: {stats['total_objects']}")
        print(f"  轨迹数量: {len(processor.track_history)}")
        print(f"  处理时间: {stats['processing_time']:.2f}秒")
        print(f"  平均FPS: {stats['avg_fps']:.1f}")
        print("\n各类别统计:")
        for class_name, count in stats['class_counts'].items():
            if count > 0:
                print(f"  {class_name}: {count}")


if __name__ == "__main__":
    main() 