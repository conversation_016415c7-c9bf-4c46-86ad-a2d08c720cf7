{"time":"2025-07-20T20:12:37.4743557+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-20T20:12:38.9877419+08:00","level":"INFO","msg":"stream: created new stream","id":"0vdwthey"}
{"time":"2025-07-20T20:12:38.9877419+08:00","level":"INFO","msg":"stream: started","id":"0vdwthey"}
{"time":"2025-07-20T20:12:38.9877419+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"0vdwthey"}
{"time":"2025-07-20T20:12:38.9877419+08:00","level":"INFO","msg":"handler: started","stream_id":"0vdwthey"}
{"time":"2025-07-20T20:12:38.9877419+08:00","level":"INFO","msg":"sender: started","stream_id":"0vdwthey"}
{"time":"2025-07-20T20:14:09.7505487+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:14:24.7491591+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:14:39.74931+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
