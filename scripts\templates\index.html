
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交通目标检测与计数系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .upload-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .upload-section.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .upload-btn:hover {
            transform: translateY(-2px);
        }
        .result-section {
            display: none;
            margin-top: 30px;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .processed-image {
            max-width: 100%;
            max-height: 500px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .statistics {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .statistics h3 {
            margin-top: 0;
            color: #333;
        }
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            margin: 0 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 交通目标检测与计数系统</h1>
            <p>基于YOLOv5的智能交通监控系统</p>
        </div>
        
        <div class="content">
            <div class="upload-section" id="uploadSection">
                <h2>📁 上传文件</h2>
                <p>支持图片格式：PNG, JPG, JPEG, GIF</p>
                <p>支持视频格式：MP4, AVI, MOV</p>
                <br>
                <input type="file" id="fileInput" class="file-input" accept="image/*,video/*">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    📤 选择文件
                </button>
                <p id="dragText">或拖拽文件到此处</p>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在处理中，请稍候...</p>
            </div>
            
            <div class="result-section" id="resultSection">
                <h2>📊 处理结果</h2>
                <div class="image-container" id="imageContainer"></div>
                <div class="statistics" id="statistics"></div>
                <div class="controls" id="controls"></div>
            </div>
        </div>
    </div>

    <script>
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');
        const loading = document.getElementById('loading');
        const resultSection = document.getElementById('resultSection');
        const imageContainer = document.getElementById('imageContainer');
        const statistics = document.getElementById('statistics');
        const controls = document.getElementById('controls');

        // 拖拽上传
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            loading.style.display = 'block';
            resultSection.style.display = 'none';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.error) {
                    showError(data.error);
                    return;
                }

                if (data.type === 'image') {
                    showImageResult(data);
                } else if (data.type === 'video') {
                    showVideoResult(data);
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                showError('处理文件时出错: ' + error.message);
            });
        }

        function showImageResult(data) {
            imageContainer.innerHTML = `
                <img src="${data.processed_image}" alt="处理结果" class="processed-image">
            `;
            
            showStatistics(data.statistics, data.class_counts);
            showControls(data.filename);
            resultSection.style.display = 'block';
        }

        function showVideoResult(data) {
            // 处理视频
            fetch('/process_video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({filename: data.filename})
            })
            .then(response => response.json())
            .then(result => {
                if (result.error) {
                    showError(result.error);
                    return;
                }
                
                imageContainer.innerHTML = `
                    <h3>🎬 视频处理完成</h3>
                    <p>原始文件: ${result.filename}</p>
                    <p>处理后文件: ${result.processed_filename}</p>
                `;
                
                showStatistics(result.statistics, result.statistics.class_counts);
                showControls(result.processed_filename, true);
                resultSection.style.display = 'block';
            })
            .catch(error => {
                showError('处理视频时出错: ' + error.message);
            });
        }

        function showStatistics(frameStats, classCounts) {
            let html = '<h3>📈 检测统计</h3>';
            
            if (frameStats) {
                html += '<div class="stat-grid">';
                html += `<div class="stat-item">
                    <div class="stat-value">${frameStats.objects_detected || 0}</div>
                    <div class="stat-label">当前帧目标数</div>
                </div>`;
                html += `<div class="stat-item">
                    <div class="stat-value">${frameStats.tracks_active || 0}</div>
                    <div class="stat-label">活跃轨迹数</div>
                </div>`;
                html += '</div>';
            }
            
            if (classCounts) {
                html += '<h4>各类别统计:</h4>';
                html += '<div class="stat-grid">';
                for (const [className, count] of Object.entries(classCounts)) {
                    if (count > 0) {
                        html += `<div class="stat-item">
                            <div class="stat-value">${count}</div>
                            <div class="stat-label">${className}</div>
                        </div>`;
                    }
                }
                html += '</div>';
            }
            
            statistics.innerHTML = html;
        }

        function showControls(filename, isVideo = false) {
            let html = '';
            if (isVideo) {
                html += `<a href="/download/${filename}" class="btn">📥 下载处理后的视频</a>`;
            }
            html += `<button class="btn" onclick="resetStats()">🔄 重置统计</button>`;
            controls.innerHTML = html;
        }

        function showError(message) {
            resultSection.style.display = 'block';
            resultSection.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        function resetStats() {
            fetch('/api/reset')
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert('统计信息已重置');
                    location.reload();
                }
            })
            .catch(error => {
                alert('重置统计时出错: ' + error.message);
            });
        }
    </script>
</body>
</html>
    