{"total_images": 9, "total_objects": 30, "class_counts": {"tricycle": 30, "car": 0, "person": 0}, "detection_details": [{"image_name": "frame_00001", "total_objects": 3, "class_counts": {"tricycle": 3, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp\\frame_00001.jpg", "exp_dir": "exp"}, {"image_name": "frame_00002", "total_objects": 5, "class_counts": {"tricycle": 5, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp2\\frame_00002.jpg", "exp_dir": "exp2"}, {"image_name": "frame_00003", "total_objects": 6, "class_counts": {"tricycle": 6, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp2\\frame_00003.jpg", "exp_dir": "exp2"}, {"image_name": "frame_00004", "total_objects": 3, "class_counts": {"tricycle": 3, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp2\\frame_00004.jpg", "exp_dir": "exp2"}, {"image_name": "frame_00005", "total_objects": 5, "class_counts": {"tricycle": 5, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp2\\frame_00005.jpg", "exp_dir": "exp2"}, {"image_name": "frame_00006", "total_objects": 2, "class_counts": {"tricycle": 2, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp2\\frame_00006.jpg", "exp_dir": "exp2"}, {"image_name": "frame_00007", "total_objects": 2, "class_counts": {"tricycle": 2, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp2\\frame_00007.jpg", "exp_dir": "exp2"}, {"image_name": "frame_00008", "total_objects": 4, "class_counts": {"tricycle": 4, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp2\\frame_00008.jpg", "exp_dir": "exp2"}, {"image_name": "frame_00000", "total_objects": 0, "class_counts": {"tricycle": 0, "car": 0, "person": 0}, "image_path": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\runs\\detect\\exp3\\frame_00000.jpg", "exp_dir": "exp3"}]}