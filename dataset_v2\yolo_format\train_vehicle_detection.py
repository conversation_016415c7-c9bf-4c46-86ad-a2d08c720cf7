#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆检测训练脚本 - 数据集V2
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

# 导入YOLOv5训练模块
import train

if __name__ == "__main__":
    # 训练参数
    data_yaml = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\dataset_v2\yolo_format\dataset.yaml"
    weights = "yolov5n.pt"  # 预训练权重
    epochs = 100
    batch_size = 16
    img_size = 640
    
    print("🚀 开始训练车辆检测模型...")
    print(f"📊 数据集: {data_yaml}")
    print(f"🎯 预训练权重: {weights}")
    print(f"⏱️ 训练轮数: {epochs}")
    print(f"📦 批次大小: {batch_size}")
    print(f"🖼️ 图片尺寸: {img_size}")
    print("-" * 50)
    
    # 开始训练
    train.run(
        data=data_yaml,
        weights=weights,
        epochs=epochs,
        batch_size=batch_size,
        imgsz=img_size,
        project="runs/train",
        name="vehicle_detection_v2",
        exist_ok=True,
        save_period=10,
        patience=20,
        device="0"  # 使用GPU
    )
    
    print("✅ 训练完成!")
