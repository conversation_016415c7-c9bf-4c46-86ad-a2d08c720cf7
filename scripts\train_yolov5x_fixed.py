#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版YOLOv5x训练脚本
直接在数据集目录下运行，避免路径解析问题
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🚀 修复版YOLOv5x训练系统")
    print("=" * 60)
    
    # 使用绝对路径
    dataset_yaml = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\dataset_v2\yolo_format\dataset.yaml"
    weights_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\models\yolov5x.pt"
    yolo_dir = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5"
    dataset_dir = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\dataset_v2\yolo_format"
    
    # 检查文件是否存在
    if not Path(weights_path).exists():
        print(f"❌ YOLOv5x权重文件不存在: {weights_path}")
        return
    
    if not Path(dataset_yaml).exists():
        print(f"❌ 数据集配置文件不存在: {dataset_yaml}")
        return
    
    if not Path(yolo_dir).exists():
        print(f"❌ YOLOv5目录不存在: {yolo_dir}")
        return
    
    print(f"✅ 找到YOLOv5x权重: {weights_path}")
    print(f"✅ 找到数据集配置: {dataset_yaml}")
    print(f"✅ 找到YOLOv5目录: {yolo_dir}")
    
    # 检查数据集结构
    train_images = Path(dataset_dir) / "train" / "images"
    val_images = Path(dataset_dir) / "val" / "images"
    
    if not train_images.exists():
        print(f"❌ 训练图片目录不存在: {train_images}")
        return
    
    if not val_images.exists():
        print(f"❌ 验证图片目录不存在: {val_images}")
        return
    
    # 统计图片数量
    train_count = len(list(train_images.glob("*.jpg")))
    val_count = len(list(val_images.glob("*.jpg")))
    
    print(f"📊 数据集统计:")
    print(f"  训练图片: {train_count} 张")
    print(f"  验证图片: {val_count} 张")
    print(f"  总图片: {train_count + val_count} 张")
    
    # 训练参数
    epochs = 1000
    batch_size = 2
    img_size = 1024
    patience = 200
    
    print(f"\n📊 训练参数:")
    print(f"  训练轮数: {epochs}")
    print(f"  批次大小: {batch_size}")
    print(f"  图片尺寸: {img_size}x{img_size}")
    print(f"  早停耐心: {patience}")
    
    # 切换到数据集目录
    os.chdir(dataset_dir)
    
    # 构建训练命令 - 使用相对路径
    cmd = f"""python "{yolo_dir}/train.py" --data dataset.yaml --weights "{weights_path}" --epochs {epochs} --batch-size {batch_size} --img {img_size} --project "{yolo_dir}/runs/train" --name vehicle_detection_yolov5x_v2 --exist-ok --device 0 --patience {patience} --save-period 10"""
    
    print(f"\n🔧 执行训练命令:")
    print(cmd)
    print("\n" + "=" * 60)
    
    # 询问是否开始训练
    response = input("\n是否开始YOLOv5x训练? (y/n): ").lower().strip()
    
    if response == 'y':
        try:
            # 设置环境变量禁用wandb
            env = os.environ.copy()
            env['WANDB_MODE'] = 'disabled'
            
            print("🚀 开始训练...")
            print("⏳ 训练可能需要较长时间，请耐心等待...")
            
            # 执行训练
            result = subprocess.run(cmd, shell=True, check=True, env=env)
            print("\n✅ YOLOv5x训练完成!")
            
            # 显示训练结果
            train_dir = Path(yolo_dir) / "runs" / "train" / "vehicle_detection_yolov5x_v2"
            if train_dir.exists():
                print(f"\n📁 训练结果保存在: {train_dir}")
                
                # 检查最佳权重
                best_weights = train_dir / "weights" / "best.pt"
                last_weights = train_dir / "weights" / "last.pt"
                
                if best_weights.exists():
                    print(f"🏆 最佳权重: {best_weights}")
                    print(f"📊 文件大小: {best_weights.stat().st_size / (1024*1024):.1f} MB")
                if last_weights.exists():
                    print(f"📝 最新权重: {last_weights}")
                    print(f"📊 文件大小: {last_weights.stat().st_size / (1024*1024):.1f} MB")
            
            print("\n" + "=" * 60)
            print("🎉 YOLOv5x训练完成!")
            print("\n📋 下一步操作:")
            print("1. 启动Web界面: python ../../../scripts/web_interface.py")
            print("2. 访问地址: http://localhost:5000")
            
            print("\n💡 YOLOv5x优势:")
            print("- 更高的检测精度")
            print("- 更好的小目标检测能力")
            print("- 更强的特征提取能力")
            print("- 适合复杂场景")
            
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 训练失败: {e}")
            print("\n💡 建议:")
            print("1. 检查GPU内存是否足够")
            print("2. 尝试减小batch_size")
            print("3. 尝试减小img_size")
            print("4. 检查数据集路径是否正确")
        except KeyboardInterrupt:
            print("\n⚠️ 训练被用户中断")
        except Exception as e:
            print(f"\n❌ 训练出现未知错误: {e}")
    else:
        print("\n⚠️ 训练已取消")

if __name__ == "__main__":
    main() 