{"time":"2025-07-20T19:36:25.0970142+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-20T19:36:27.3189505+08:00","level":"INFO","msg":"stream: created new stream","id":"hqjus5ee"}
{"time":"2025-07-20T19:36:27.3189505+08:00","level":"INFO","msg":"stream: started","id":"hqjus5ee"}
{"time":"2025-07-20T19:36:27.3189505+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"hqjus5ee"}
{"time":"2025-07-20T19:36:27.3195274+08:00","level":"INFO","msg":"handler: started","stream_id":"hqjus5ee"}
{"time":"2025-07-20T19:36:27.3195274+08:00","level":"INFO","msg":"sender: started","stream_id":"hqjus5ee"}
{"time":"2025-07-20T19:36:42.8977759+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:36:57.8977917+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:37:12.8982274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:37:27.8986488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:37:42.8980168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:37:57.897728+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:38:12.8981087+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:38:27.8978741+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:38:42.8978359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:38:57.8975484+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:39:12.897678+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:39:27.8976074+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:39:42.8977147+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:39:57.8975352+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:40:12.8975355+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:40:27.8980022+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:40:42.8978928+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:40:57.8975132+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:41:12.8977622+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:41:27.8975632+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:41:42.8974791+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:41:57.8982578+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:42:12.8977405+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:42:27.8979641+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:42:42.8977858+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:42:57.8978158+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:43:12.8978687+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:43:27.897867+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:43:42.897916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:43:57.8978306+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:44:12.8979806+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:44:27.8975081+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
