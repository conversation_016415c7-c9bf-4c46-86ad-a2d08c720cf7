{"time":"2025-07-20T19:36:25.0970142+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-20T19:36:27.3189505+08:00","level":"INFO","msg":"stream: created new stream","id":"hqjus5ee"}
{"time":"2025-07-20T19:36:27.3189505+08:00","level":"INFO","msg":"stream: started","id":"hqjus5ee"}
{"time":"2025-07-20T19:36:27.3189505+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"hqjus5ee"}
{"time":"2025-07-20T19:36:27.3195274+08:00","level":"INFO","msg":"handler: started","stream_id":"hqjus5ee"}
{"time":"2025-07-20T19:36:27.3195274+08:00","level":"INFO","msg":"sender: started","stream_id":"hqjus5ee"}
{"time":"2025-07-20T19:36:42.8977759+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:36:57.8977917+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:37:12.8982274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:37:27.8986488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:37:42.8980168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:37:57.897728+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:38:12.8981087+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:38:27.8978741+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:38:42.8978359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:38:57.8975484+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:39:12.897678+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:39:27.8976074+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:39:42.8977147+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:39:57.8975352+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:40:12.8975355+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:40:27.8980022+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:40:42.8978928+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:40:57.8975132+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:41:12.8977622+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:41:27.8975632+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:41:42.8974791+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:41:57.8982578+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:42:12.8977405+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:42:27.8979641+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:42:42.8977858+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:42:57.8978158+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:43:12.8978687+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:43:27.897867+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:43:42.897916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:43:57.8978306+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:44:12.8979806+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:44:27.8975081+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:44:42.8979093+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:44:57.8979352+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:45:12.8975195+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:45:27.8977574+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:45:42.8983426+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:45:57.8999435+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:46:12.8976086+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:46:27.8980106+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:46:42.8981883+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:46:57.898389+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:47:12.899916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:47:27.8988537+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:47:42.8975572+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:47:57.8979021+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:48:12.8975623+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:48:27.8976717+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:48:42.897487+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:48:57.898257+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:49:12.8981517+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:49:27.8979477+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:49:42.8981739+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:49:57.8979669+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:50:12.8979569+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:50:27.8980683+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:50:42.897533+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:50:57.8977381+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:51:12.8977019+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:51:27.8980003+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:51:42.8983359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:51:57.8978132+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:52:12.8978558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:52:27.8975586+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:52:42.8979826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:52:57.8975817+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:12.8978116+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:27.8980088+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:42.8978187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:57.8976905+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:12.8981236+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:27.8976351+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:42.8979734+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:57.8979001+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:55:12.8976957+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:55:27.8977885+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:55:42.8979353+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:55:57.8976733+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:56:12.8974761+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:56:27.8975976+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:56:42.8976961+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:56:57.8977962+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:12.8975781+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:27.8976581+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:42.8976437+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:57.8977735+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:12.8978299+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:27.8978767+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:42.8977879+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:57.8979656+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:12.8979904+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:27.8981062+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:42.8975579+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:57.8979724+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:12.8975886+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:27.8981478+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:42.8979518+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:57.898024+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:12.8986834+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:27.8977666+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:42.8978379+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:57.8985217+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:12.897983+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:27.8975703+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:42.8976681+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:57.8983129+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:12.8979557+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:27.8981837+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:42.89767+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:57.8977198+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:04:12.8974557+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:04:27.8980573+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:04:42.8978876+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:04:57.897893+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:12.8978511+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:27.8980046+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:42.8975765+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:57.8979541+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:12.8979185+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:27.8979703+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:42.8979647+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:57.8979179+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:12.8977223+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:27.8977514+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:42.8977129+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:57.8977949+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:08:12.8979624+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:08:27.8977906+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:08:42.8979702+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:08:57.8977473+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:09:12.8978594+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:09:27.8976902+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:09:42.8979303+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:09:58.2164864+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:10:12.8976359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:10:27.8977941+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:10:42.8979708+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:10:57.8981024+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:11:12.8980057+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:11:27.8978881+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:11:42.8978905+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:11:57.897769+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:12:12.8975763+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:12:27.8974726+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:12:42.8979496+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:12:57.8976131+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:13:12.8988863+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:13:27.8976666+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:13:42.8989454+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:13:57.897974+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:14:12.8978211+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:14:27.8977668+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:14:42.8978699+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:14:57.8979103+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:15:12.8978908+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:15:27.897596+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:15:42.8982434+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:15:57.8975454+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:16:12.8973951+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:16:27.8977036+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:16:42.8982492+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:16:57.8981428+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:17:12.8976098+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:17:27.8977364+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:17:42.8979057+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:17:57.8978553+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:18:12.8979364+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:18:27.8989052+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:18:42.8974697+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:18:57.8979434+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:19:12.8977393+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:19:27.8993151+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:19:42.8978305+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:19:57.897804+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:20:12.8979822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:20:27.897599+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:20:43.7696257+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:20:57.8975258+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:21:12.8975992+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:21:27.8975187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:21:42.8982209+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:21:57.8977723+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:22:12.8979187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:22:27.8981032+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:22:42.8979101+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:22:57.8980029+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:23:12.8976418+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:23:27.8980598+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:23:42.8982617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:23:57.8978764+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:24:12.898042+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:24:27.8981497+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:24:42.8979023+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:24:57.8978753+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:25:12.8983115+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:25:27.8979441+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:25:42.8978932+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:25:57.8977605+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:26:12.8976884+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:26:27.8981574+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:26:42.8980802+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:26:57.8977561+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:27:12.8977443+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:27:27.8978694+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:27:42.8978852+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:27:57.8978811+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:28:12.8979999+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:28:27.897502+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:28:42.8979073+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:28:57.8976313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:29:12.8979452+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:29:27.8978336+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:29:42.8980545+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:29:57.8979142+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:30:12.8980501+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:30:27.8981932+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:30:42.8979239+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:30:57.8979828+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:31:12.8978904+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:31:27.8977361+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:31:42.89813+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:31:57.8980834+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:32:12.8977775+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:32:27.8978906+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:32:42.8978649+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:32:57.8980147+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:33:12.8981329+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:33:27.8978053+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:33:42.8978139+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:33:57.8980842+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:34:12.8979849+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:34:27.902161+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:34:42.8976535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:34:57.8979085+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:35:12.8978749+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:35:27.8975117+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:35:42.8981257+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:35:57.8979335+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:36:12.8978931+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:36:27.8979471+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:36:42.8976828+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:36:57.8976851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:37:12.897729+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:37:27.897927+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:37:42.8978195+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:37:57.8979204+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:38:12.8979054+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:38:27.8992596+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:38:42.8978918+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:38:57.8977772+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:39:12.897843+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:39:27.8975585+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:39:42.8977409+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:39:57.8977889+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:40:12.8978196+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:40:27.8976952+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:40:42.8979908+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:40:57.8977599+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:41:12.8977821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:41:27.8976379+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:41:42.8979925+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:41:57.8977816+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:42:12.8978092+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:42:27.8975698+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:42:42.8981882+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:42:57.8976776+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:43:12.8980012+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:43:27.8980718+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:43:42.8978669+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:43:57.897461+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:44:12.8978427+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:44:27.8977875+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:44:42.897478+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:44:57.8980274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:45:12.8978336+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:45:27.897911+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:45:42.8977089+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:53:47.5298628+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:53:51.2483112+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/willislipro-other/train/hqjus5ee/file_stream\": dial tcp: lookup api.wandb.ai: no such host"}
{"time":"2025-07-20T20:53:57.8981285+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:54:12.897974+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:54:27.8975786+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:54:42.8978616+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:54:57.8975994+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:55:12.8978988+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:55:27.8977641+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:55:42.8976875+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:55:57.8976032+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:56:12.8977716+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:56:27.8976385+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:56:42.8977805+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:56:57.8980896+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:57:12.8977266+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:57:27.897796+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:57:42.8976074+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:57:57.897902+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:58:12.8977199+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:58:27.8979+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:58:42.8978802+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:58:57.8979242+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
