weights: yolov5n.pt
cfg: models/yolov5n.yaml
data: ../../../dataset_v2/yolo_format/dataset.yaml
hyp: data\hyps\hyp.scratch-low.yaml
epochs: 100
batch_size: 16
imgsz: 640
rect: false
resume: false
nosave: false
noval: false
noautoanchor: false
evolve: null
bucket: ''
cache: null
image_weights: false
device: '0'
multi_scale: false
single_cls: false
optimizer: SGD
sync_bn: false
workers: 8
project: runs/train
name: vehicle_detection_v2
exist_ok: true
quad: false
cos_lr: false
label_smoothing: 0.0
patience: 100
freeze:
- 0
save_period: -1
local_rank: -1
entity: null
upload_dataset: false
bbox_interval: -1
artifact_alias: latest
save_dir: runs\train\vehicle_detection_v2
