absl-py==2.2.2
albumentations==1.3.0
annotated-types==0.7.0
Bottleneck==1.3.7
Brotli==1.0.9
cachetools==5.5.2
certifi==2024.8.30
certifi==2025.4.26
cffi==1.17.0
charset-normalizer==3.4.0
click==8.1.8
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.1.1
cycler==0.12.1
Cython==3.0.11
einops==0.8.0
eval_type_backport==0.2.2
filelock==3.16.1
flatbuffers==25.2.10
fonttools==4.53.1
fsspec==2024.6.1
fsspec==2025.3.0
gitdb==4.0.12
GitPython==3.1.44
google-auth==2.40.1
google-auth-oauthlib==1.0.0
GPUtil==1.4.0
grpcio==1.70.0
h2==4.1.0
hpack==4.0.0
hub-sdk==0.0.20
huggingface_hub==0.24.6
humanfriendly==10.0
hyperframe==6.0.1
idna==3.10
imageio==2.35.1
importlib_metadata==8.5.0
Jinja2==3.1.4
joblib==1.4.2
kiwisolver==1.4.5
labelImg==1.8.6
lap==0.4.0
lazy_loader==0.4
lxml==5.3.0
lxml==6.0.0
Markdown==3.7
MarkupSafe==2.1.5
matplotlib==3.6.2
mkl-service==2.4.0
mpmath==1.3.0
munkres==1.1.4
networkx==3.1
numexpr==2.7.3
numpy==1.24.4
oauthlib==3.2.2
onnx==1.14.0
onnxruntime-gpu==1.16.0
opencv-python==4.6.0
opencv-python-headless==*********
packaging==25.0
pandas==1.5.3
pandas==2.0.3
Pillow==9.3.0
pip==22.3.1
platformdirs==4.3.6
ply==3.11
pooch==1.8.2
protobuf==5.29.4
psutil==5.9.0
psutil==5.9.4
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycocotools==2.0.7
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pyparsing==3.1.4
PyQt5==5.15.9
PyQt5-sip==12.12.2
pyreadline3==3.5.4
PySocks==1.7.1
python-dateutil==2.9.0
pytz==2024.1
pytz==2025.2
PyWavelets==1.4.1
PyYAML==6.0.2
py-cpuinfo==9.0.0
qudida==0.0.4
requests==2.32.3
requests-oauthlib==2.0.0
rsa==4.9.1
safetensors==0.4.5
scikit-image==0.21.0
scikit-learn==1.3.2
scipy==1.10.0
seaborn==0.12.2
seaborn==0.13.2
sentry-sdk==2.32.0
setuptools==75.3.0
sip==6.7.12
six==1.16.0
smmap==5.0.2
sympy==1.13.3
tensorboard==2.12.2
tensorboard-data-server==0.7.2
tensorboard-plugin-wit==1.8.1
thop==0.1.1-2209072238
threadpoolctl==3.5.0
tifffile==2023.7.10
timm==1.0.11
toml==0.10.2
tomli==2.0.2
torch==2.4.1+cu121
torchaudio==2.4.1+cu121
torchvision==0.19.1+cu121
tornado==6.4.1
tqdm==4.64.1
tqdm==4.66.5
typing_extensions==4.12.2
tzdata==2025.2
ultralytics==8.3.43
ultralytics-thop==2.0.14
unicodedata2==15.1.0
urllib3==2.2.3
wandb==0.21.0
Werkzeug==3.0.6
wheel==0.45.1
win_inet_pton==1.1.0
zipp==3.20.2
zstandard==0.19.0
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
importlib_resources==6.4.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.1
platformdirs==4.2.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.43.0
zipp==3.19.2
