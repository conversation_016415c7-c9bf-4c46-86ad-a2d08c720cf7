{"os": "Windows-11-10.0.26100-SP0", "python": "CPython 3.12.7", "startedAt": "2025-07-20T13:01:37.747835Z", "args": ["--device", "0", "--epochs", "5", "--batch-size", "4", "--imgsz", "640", "--workers", "0"], "program": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\train_x.py", "codePath": "train_x.py", "codePathLocal": "train_x.py", "email": "<EMAIL>", "root": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5", "host": "deepmind", "executable": "D:\\Anaconda3\\python.exe", "cpu_count": 14, "cpu_count_logical": 20, "gpu": "NVIDIA GeForce RTX 3060 Laptop GPU", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "16962281472"}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 3060 Laptop GPU", "memoryTotal": "6442450944", "cudaCores": 3840, "architecture": "Ampere", "uuid": "GPU-96e084a5-f3f5-ae24-8d91-e1f8c5adec6b"}], "cudaVersion": "12.8", "writerId": "bcx7pnsgeq6ca7wr4n6dhuf79bxgzo7c"}