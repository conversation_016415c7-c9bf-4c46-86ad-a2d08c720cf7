Defaulting to user installation because normal site-packages is not writeable
Collecting qudida
  Downloading qudida-0.0.4-py3-none-any.whl.metadata (1.5 kB)
Requirement already satisfied: numpy>=0.18.0 in d:\anaconda3\lib\site-packages (from qudida) (1.26.4)
Requirement already satisfied: scikit-learn>=0.19.1 in d:\anaconda3\lib\site-packages (from qudida) (1.5.1)
Requirement already satisfied: typing-extensions in c:\users\<USER>\appdata\roaming\python\python312\site-packages (from qudida) (4.12.2)
Requirement already satisfied: opencv-python-headless>=4.0.1 in c:\users\<USER>\appdata\roaming\python\python312\site-packages (from qudida) (*********)
Requirement already satisfied: scipy>=1.6.0 in c:\users\<USER>\appdata\roaming\python\python312\site-packages (from scikit-learn>=0.19.1->qudida) (1.13.0)
Requirement already satisfied: joblib>=1.2.0 in d:\anaconda3\lib\site-packages (from scikit-learn>=0.19.1->qudida) (1.4.2)
Requirement already satisfied: threadpoolctl>=3.1.0 in d:\anaconda3\lib\site-packages (from scikit-learn>=0.19.1->qudida) (3.5.0)
Downloading qudida-0.0.4-py3-none-any.whl (3.5 kB)
Installing collected packages: qudida
Successfully installed qudida-0.0.4
