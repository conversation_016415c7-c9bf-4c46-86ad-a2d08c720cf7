import os
import glob
from PIL import Image

# 数据目录
DATASET_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../dataset_v1'))

# 获取所有图片文件
image_files = glob.glob(os.path.join(DATASET_DIR, '*.jpg'))

# 检查classes.txt
classes_path = os.path.join(DATASET_DIR, 'classes.txt')
if os.path.exists(classes_path):
    with open(classes_path, 'r', encoding='utf-8') as f:
        class_list = [line.strip() for line in f.readlines() if line.strip()]
else:
    class_list = []

print(f"检测到类别: {class_list if class_list else '未检测到类别文件'}")

for img_path in image_files:
    label_path = img_path.replace('.jpg', '.txt')
    if not os.path.exists(label_path):
        print(f"跳过无标签图片: {img_path}")
        continue
    # 获取图片分辨率
    with Image.open(img_path) as im:
        w, h = im.size
    new_lines = []
    with open(label_path, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) < 5:
                print(f"标签格式异常: {label_path} -> {line.strip()}")
                continue
            # 假设VOC格式: class_id xmin ymin xmax ymax
            class_id = int(parts[0])
            xmin, ymin, xmax, ymax = map(float, parts[1:5])
            # 转为YOLO格式: class_id cx cy w h (归一化)
            cx = (xmin + xmax) / 2.0 / w
            cy = (ymin + ymax) / 2.0 / h
            bw = (xmax - xmin) / w
            bh = (ymax - ymin) / h
            new_line = f"{class_id} {cx:.6f} {cy:.6f} {bw:.6f} {bh:.6f}"
            new_lines.append(new_line)
    # 覆盖写回YOLO格式
    with open(label_path, 'w', encoding='utf-8') as f:
        for l in new_lines:
            f.write(l + '\n')
    print(f"已转换: {os.path.basename(label_path)} [{w}x{h}]")

print("全部转换完成！") 