import cv2
import os
import numpy as np

# 视频路径（相对路径，从项目根目录开始）
video_path = "../data/3007580-uhd_3840_2160_30fps.mp4"
# 输出图片文件夹（相对路径）
output_dir = "../images"

# 检查输出目录状态
print(f"=== 开始前检查 ===")
print(f"当前工作目录: {os.getcwd()}")
print(f"输出目录: {output_dir}")
print(f"输出目录是否存在: {os.path.exists(output_dir)}")
if os.path.exists(output_dir):
    files = os.listdir(output_dir)
    print(f"输出目录内容: {files}")
    print(f"输出目录文件数量: {len(files)}")
else:
    print("输出目录不存在")

# 创建输出文件夹
os.makedirs(output_dir, exist_ok=True)

# 打开视频
cap = cv2.VideoCapture(video_path)
if not cap.isOpened():
    raise IOError(f"无法打开视频文件: {video_path}")

frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
fps = cap.get(cv2.CAP_PROP_FPS)
print(f"视频总帧数: {frame_count}, 帧率: {fps}")

# 目标帧数
num_frames = 30
# 计算等间隔抽帧的帧索引
frame_indices = np.linspace(0, frame_count - 1, num_frames, dtype=int)

# 开始读取并保存帧
for i, frame_index in enumerate(frame_indices):
    # 直接跳转到指定帧位置
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
    ret, frame = cap.read()
    if not ret:
        print(f"无法读取第 {frame_index} 帧")
        continue

    # 保存图片
    img_name = f"frame_{i:05d}.jpg"
    img_path = os.path.join(output_dir, img_name)
    success = cv2.imwrite(img_path, frame)
    if not success:
        print(f"写入失败: {img_path}")
    else:
        print(f"保存: {img_path}")

cap.release()

# 检查输出目录状态
print(f"\n=== 结束后检查 ===")
print(f"输出目录: {output_dir}")
print(f"输出目录是否存在: {os.path.exists(output_dir)}")
if os.path.exists(output_dir):
    files = os.listdir(output_dir)
    print(f"输出目录内容: {files}")
    print(f"输出目录文件数量: {len(files)}")
    if len(files) > 0:
        print(f"第一个文件: {files[0]}")
        first_file_path = os.path.join(output_dir, files[0])
        print(f"第一个文件是否存在: {os.path.exists(first_file_path)}")
        print(f"第一个文件大小: {os.path.getsize(first_file_path)} bytes")
else:
    print("输出目录不存在")

print(f"抽帧完成，共保存 {num_frames} 张图片到 {output_dir}/") 