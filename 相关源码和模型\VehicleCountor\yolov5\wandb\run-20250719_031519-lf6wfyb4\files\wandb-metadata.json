{"os": "Windows-10-10.0.26100-SP0", "python": "CPython 3.8.20", "startedAt": "2025-07-18T19:15:19.302366Z", "args": ["--img", "1024", "--batch", "4", "--epochs", "100", "--data", "../../../dataset_v1/dataset.yaml", "--weights", "yolov5n.pt", "--device", "0", "--nosave"], "program": "train.py", "codePath": "train.py", "codePathLocal": "train.py", "root": "D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5", "host": "deepmind", "executable": "C:\\Users\\<USER>\\.conda\\envs\\yoloobb\\python.exe", "cpu_count": 14, "cpu_count_logical": 20, "gpu": "NVIDIA GeForce RTX 3060 Laptop GPU", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "16962281472"}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 3060 Laptop GPU", "memoryTotal": "6442450944", "cudaCores": 3840, "architecture": "Ampere", "uuid": "GPU-96e084a5-f3f5-ae24-8d91-e1f8c5adec6b"}], "cudaVersion": "12.8", "writerId": "bwxbekw0569pyv94soa20waz0ah39r83"}