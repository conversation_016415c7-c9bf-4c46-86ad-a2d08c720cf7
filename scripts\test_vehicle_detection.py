#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆检测测试脚本
使用新的训练好的模型进行测试
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

from detect import run

if __name__ == "__main__":
    # 测试参数
    weights = "runs/train/exp21/weights/last.pt"  # 训练好的模型
    source = "../../../simple_dataset/val/images"  # 测试图片目录
    conf_thres = 0.25
    iou_thres = 0.45
    
    print("🔍 开始车辆检测测试...")
    print(f"🎯 模型权重: {weights}")
    print(f"📸 测试图片: {source}")
    print(f"📊 置信度阈值: {conf_thres}")
    print(f"🔗 IoU阈值: {iou_thres}")
    print("-" * 50)
    
    # 开始推理
    run(
        weights=weights,
        source=source,
        conf_thres=conf_thres,
        iou_thres=iou_thres,
        project="runs/detect",
        name="vehicle_detection_test",
        exist_ok=True,
        save_txt=True,
        save_conf=True
    )
    
    print("✅ 测试完成!")
