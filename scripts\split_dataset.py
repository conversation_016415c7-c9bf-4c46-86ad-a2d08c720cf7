import os
import random
import shutil
from glob import glob

# 数据目录
DATASET_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../dataset_v1'))
SPLITS = ['train', 'val', 'test']
RATIOS = [0.8, 0.1, 0.1]

# 创建目标目录结构
def make_dirs():
    for split in SPLITS:
        os.makedirs(os.path.join(DATASET_DIR, f'images/{split}'), exist_ok=True)
        os.makedirs(os.path.join(DATASET_DIR, f'labels/{split}'), exist_ok=True)

# 获取所有图片文件
image_files = glob(os.path.join(DATASET_DIR, '*.jpg'))
image_files.sort()
random.seed(42)
random.shuffle(image_files)

total = len(image_files)
train_num = int(total * RATIOS[0])
val_num = int(total * RATIOS[1])
train_files = image_files[:train_num]
val_files = image_files[train_num:train_num+val_num]
test_files = image_files[train_num+val_num:]

split_map = [(train_files, 'train'), (val_files, 'val'), (test_files, 'test')]

make_dirs()

for files, split in split_map:
    for img_path in files:
        base = os.path.basename(img_path)
        label_path = img_path.replace('.jpg', '.txt')
        # 复制图片
        dst_img = os.path.join(DATASET_DIR, f'images/{split}', base)
        shutil.copy2(img_path, dst_img)
        # 复制标签
        if os.path.exists(label_path):
            dst_label = os.path.join(DATASET_DIR, f'labels/{split}', base.replace('.jpg', '.txt'))
            shutil.copy2(label_path, dst_label)

print(f"划分完成！训练集: {len(train_files)}，验证集: {len(val_files)}，测试集: {len(test_files)}") 