#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新Web界面脚本
使用新的训练好的模型更新Web界面
"""

import os
import sys
import shutil
from pathlib import Path

def update_web_interface():
    """更新Web界面配置"""
    print("🔧 更新Web界面配置...")
    
    # 项目路径
    project_dir = Path(__file__).parent.parent
    web_interface_path = project_dir / "scripts" / "web_interface.py"
    
    # 新的模型路径
    new_model_path = project_dir / "相关源码和模型" / "VehicleCountor" / "yolov5" / "runs" / "train" / "exp21" / "weights" / "last.pt"
    
    if not new_model_path.exists():
        print(f"❌ 模型文件不存在: {new_model_path}")
        return
    
    print(f"✅ 找到模型文件: {new_model_path}")
    
    # 读取Web界面文件
    with open(web_interface_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新模型路径
    old_model_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\runs\train\exp21\weights\last.pt"
    new_model_path_str = str(new_model_path.absolute())
    
    # 替换模型路径
    content = content.replace(old_model_path, new_model_path_str)
    
    # 更新类别信息（只保留car和person）
    content = content.replace("['car', 'person', 'tricycle']", "['car', 'person']")
    
    # 写回文件
    with open(web_interface_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Web界面配置已更新!")
    print(f"📊 新模型路径: {new_model_path_str}")
    print("🎯 类别: ['car', 'person']")

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建测试脚本...")
    
    test_script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆检测测试脚本
使用新的训练好的模型进行测试
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

from detect import run

if __name__ == "__main__":
    # 测试参数
    weights = "runs/train/exp21/weights/last.pt"  # 训练好的模型
    source = "../../../simple_dataset/val/images"  # 测试图片目录
    conf_thres = 0.25
    iou_thres = 0.45
    
    print("🔍 开始车辆检测测试...")
    print(f"🎯 模型权重: {weights}")
    print(f"📸 测试图片: {source}")
    print(f"📊 置信度阈值: {conf_thres}")
    print(f"🔗 IoU阈值: {iou_thres}")
    print("-" * 50)
    
    # 开始推理
    run(
        weights=weights,
        source=source,
        conf_thres=conf_thres,
        iou_thres=iou_thres,
        project="runs/detect",
        name="vehicle_detection_test",
        exist_ok=True,
        save_txt=True,
        save_conf=True
    )
    
    print("✅ 测试完成!")
'''
    
    test_script_path = Path(__file__).parent / "test_vehicle_detection.py"
    with open(test_script_path, 'w', encoding='utf-8') as f:
        f.write(test_script_content)
    
    print(f"✅ 测试脚本已创建: {test_script_path}")

def create_dataset_summary():
    """创建数据集总结"""
    print("\n📊 创建数据集总结...")
    
    dataset_v2_dir = Path(__file__).parent.parent / "dataset_v2"
    simple_dataset_dir = Path(__file__).parent.parent / "simple_dataset"
    
    summary_content = f"""# 车辆检测数据集V2总结

## 数据集信息
- 总图片数: 15张
- 类别: car, person
- 标注格式: VOC XML -> YOLO TXT

## 数据集分布
- 训练集: 12张图片 (80%)
- 验证集: 3张图片 (20%)

## 文件结构
```
dataset_v2/
├── images/          # 原始图片
├── xml/            # VOC格式标注
└── yolo_format/    # YOLO格式数据集

simple_dataset/
├── train/
│   ├── images/     # 训练图片
│   └── labels/     # YOLO格式标注
├── val/
│   ├── images/     # 验证图片
│   └── labels/     # YOLO格式标注
└── dataset.yaml    # 数据集配置
```

## 训练模型
- 模型路径: runs/train/exp21/weights/last.pt
- 训练轮数: 100
- 批次大小: 16
- 图片尺寸: 640x640

## 使用说明
1. 运行Web界面: python web_interface.py
2. 测试模型: python test_vehicle_detection.py
3. 访问地址: http://localhost:5000

## 注意事项
- 数据集较小，建议增加更多标注数据
- 可以考虑数据增强来提高模型性能
- 建议使用更大的模型（如yolov5s, yolov5m）来提升检测精度
"""
    
    summary_path = Path(__file__).parent.parent / "README_dataset_v2.md"
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    print(f"✅ 数据集总结已创建: {summary_path}")

def main():
    """主函数"""
    print("🎯 车辆检测系统更新工具")
    print("=" * 50)
    
    # 更新Web界面
    update_web_interface()
    
    # 创建测试脚本
    create_test_script()
    
    # 创建数据集总结
    create_dataset_summary()
    
    print("\n" + "=" * 50)
    print("🎉 系统更新完成!")
    print("\n📋 下一步操作:")
    print("1. 运行Web界面: python web_interface.py")
    print("2. 测试模型: python test_vehicle_detection.py")
    print("3. 访问Web界面: http://localhost:5000")
    print("\n💡 建议:")
    print("- 增加更多标注数据以提高模型性能")
    print("- 考虑使用更大的模型架构")
    print("- 添加数据增强技术")

if __name__ == "__main__":
    main() 