#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版车辆检测训练脚本 - 数据集V2
直接使用现有数据集进行训练，避免中文路径问题
"""

import os
import sys
import shutil
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

def create_simple_dataset():
    """创建简化的数据集结构"""
    print("🔧 创建简化数据集...")
    
    # 源数据集路径
    dataset_v2_dir = Path(__file__).parent.parent / "dataset_v2"
    images_dir = dataset_v2_dir / "images"
    xml_dir = dataset_v2_dir / "xml"
    
    # 创建简化的YOLO格式数据集
    simple_dataset_dir = Path(__file__).parent.parent / "simple_dataset"
    simple_dataset_dir.mkdir(exist_ok=True)
    
    # 创建子目录
    train_dir = simple_dataset_dir / "train"
    val_dir = simple_dataset_dir / "val"
    
    for dir_path in [train_dir, val_dir]:
        dir_path.mkdir(exist_ok=True)
        (dir_path / "images").mkdir(exist_ok=True)
        (dir_path / "labels").mkdir(exist_ok=True)
    
    # 获取所有图片和XML文件
    image_files = list(images_dir.glob("*.jpg"))
    xml_files = list(xml_dir.glob("*.xml"))
    
    print(f"📸 找到 {len(image_files)} 张图片")
    print(f"📄 找到 {len(xml_files)} 个XML文件")
    
    # 简单划分：前12张训练，后3张验证
    train_files = list(zip(image_files[:12], xml_files[:12]))
    val_files = list(zip(image_files[12:], xml_files[12:]))
    
    print(f"📊 数据集划分:")
    print(f"  训练集: {len(train_files)} 张图片")
    print(f"  验证集: {len(val_files)} 张图片")
    
    # 处理训练集
    print("\n🔄 处理训练集...")
    for img_file, xml_file in train_files:
        # 复制图片
        dst_img = train_dir / "images" / img_file.name
        shutil.copy2(img_file, dst_img)
        
        # 复制XML文件
        dst_xml = train_dir / "labels" / xml_file.name
        shutil.copy2(xml_file, dst_xml)
    
    # 处理验证集
    print("🔄 处理验证集...")
    for img_file, xml_file in val_files:
        # 复制图片
        dst_img = val_dir / "images" / img_file.name
        shutil.copy2(img_file, dst_img)
        
        # 复制XML文件
        dst_xml = val_dir / "labels" / xml_file.name
        shutil.copy2(xml_file, dst_xml)
    
    # 创建数据集配置文件
    yaml_content = f"""# 简化车辆检测数据集配置
path: {simple_dataset_dir.absolute()}
train: train/images
val: val/images

# 类别数量和名称
nc: 2
names: ['car', 'person']
"""
    
    yaml_path = simple_dataset_dir / "dataset.yaml"
    with open(yaml_path, 'w', encoding='utf-8') as f:
        f.write(yaml_content)
    
    print(f"✅ 简化数据集创建完成: {simple_dataset_dir}")
    return simple_dataset_dir, yaml_path

def convert_xml_to_yolo_batch():
    """批量转换XML到YOLO格式"""
    print("\n🔄 批量转换XML到YOLO格式...")
    
    simple_dataset_dir = Path(__file__).parent.parent / "simple_dataset"
    
    # 转换训练集
    train_labels_dir = simple_dataset_dir / "train" / "labels"
    train_images_dir = simple_dataset_dir / "train" / "images"
    
    for xml_file in train_labels_dir.glob("*.xml"):
        convert_single_xml_to_yolo(xml_file, train_images_dir)
    
    # 转换验证集
    val_labels_dir = simple_dataset_dir / "val" / "labels"
    val_images_dir = simple_dataset_dir / "val" / "images"
    
    for xml_file in val_labels_dir.glob("*.xml"):
        convert_single_xml_to_yolo(xml_file, val_images_dir)

def convert_single_xml_to_yolo(xml_file, images_dir):
    """转换单个XML文件到YOLO格式"""
    import xml.etree.ElementTree as ET
    
    # 读取XML
    tree = ET.parse(xml_file)
    root = tree.getroot()
    
    # 获取图片尺寸
    size = root.find('size')
    width = int(size.find('width').text)
    height = int(size.find('height').text)
    
    # 转换标注
    yolo_annotations = []
    for obj in root.findall('object'):
        name_elem = obj.find('name')
        if name_elem is None:
            continue
            
        class_name = name_elem.text
        class_mapping = {'car': 0, 'person': 1}
        class_id = class_mapping.get(class_name, -1)
        
        if class_id == -1:
            continue
        
        bbox = obj.find('bndbox')
        xmin = float(bbox.find('xmin').text)
        ymin = float(bbox.find('ymin').text)
        xmax = float(bbox.find('xmax').text)
        ymax = float(bbox.find('ymax').text)
        
        # 转换为YOLO格式
        x_center = (xmin + xmax) / 2.0 / width
        y_center = (ymin + ymax) / 2.0 / height
        w = (xmax - xmin) / width
        h = (ymax - ymin) / height
        
        yolo_annotations.append(f"{class_id} {x_center:.6f} {y_center:.6f} {w:.6f} {h:.6f}")
    
    # 保存YOLO格式标注
    yolo_file = xml_file.parent / (xml_file.stem + ".txt")
    with open(yolo_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(yolo_annotations))
    
    # 删除XML文件
    xml_file.unlink()

def start_training(yaml_path):
    """开始训练"""
    print("\n🚀 开始训练...")
    
    # 切换到YOLOv5目录
    yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
    os.chdir(yolo_dir)
    
    # 训练命令
    cmd = f"""python train.py --data {yaml_path} --weights yolov5n.pt --epochs 50 --batch-size 8 --img 640 --project runs/train --name vehicle_detection_v2_simple --exist-ok --device 0"""
    
    print(f"执行命令: {cmd}")
    os.system(cmd)

def main():
    """主函数"""
    print("🎯 简化版车辆检测训练系统")
    print("=" * 50)
    
    # 创建简化数据集
    simple_dataset_dir, yaml_path = create_simple_dataset()
    
    # 转换XML到YOLO格式
    convert_xml_to_yolo_batch()
    
    print(f"\n✅ 数据集准备完成!")
    print(f"📁 数据集路径: {simple_dataset_dir}")
    print(f"📊 配置文件: {yaml_path}")
    
    # 询问是否开始训练
    response = input("\n是否开始训练? (y/n): ").lower().strip()
    if response == 'y':
        start_training(yaml_path)
    else:
        print("训练已取消。您可以稍后手动运行训练命令。")

if __name__ == "__main__":
    main() 