{"time":"2025-07-20T20:59:23.0179046+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-20T20:59:25.1599073+08:00","level":"INFO","msg":"stream: created new stream","id":"g9e8lq01"}
{"time":"2025-07-20T20:59:25.1609612+08:00","level":"INFO","msg":"stream: started","id":"g9e8lq01"}
{"time":"2025-07-20T20:59:25.1609612+08:00","level":"INFO","msg":"handler: started","stream_id":"g9e8lq01"}
{"time":"2025-07-20T20:59:25.1615738+08:00","level":"INFO","msg":"sender: started","stream_id":"g9e8lq01"}
{"time":"2025-07-20T20:59:25.1615738+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"g9e8lq01"}
{"time":"2025-07-20T20:59:55.9112175+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T21:00:10.9075656+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
