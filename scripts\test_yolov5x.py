#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv5x 车辆检测测试脚本
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

def run_inference():
    """运行推理"""
    # 训练好的模型路径
    weights = "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\models\yolov5x.pt"
    
    # 测试图片
    source = "../../../dataset_v2/yolo_format/val/images"
    
    # 推理参数
    conf_thres = 0.25
    iou_thres = 0.45
    
    print("🔍 YOLOv5x 车辆检测推理")
    print("=" * 40)
    print(f"🎯 模型: {weights}")
    print(f"📸 测试图片: {source}")
    print(f"📊 置信度阈值: {conf_thres}")
    print(f"🔗 IoU阈值: {iou_thres}")
    
    # 构建推理命令
    cmd = f"""python detect.py --weights {weights} --source "{source}" --conf {conf_thres} --iou {iou_thres} --project runs/detect --name yolov5x_test --exist-ok --save-txt --save-conf"""
    
    # 切换到YOLOv5目录
    os.chdir(yolo_dir)
    
    # 执行推理
    os.system(cmd)
    print("✅ 推理完成!")

if __name__ == "__main__":
    run_inference()
