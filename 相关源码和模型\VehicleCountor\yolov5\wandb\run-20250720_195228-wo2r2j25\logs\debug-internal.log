{"time":"2025-07-20T19:52:29.9419568+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-20T19:52:31.6580852+08:00","level":"INFO","msg":"stream: created new stream","id":"wo2r2j25"}
{"time":"2025-07-20T19:52:31.6586262+08:00","level":"INFO","msg":"stream: started","id":"wo2r2j25"}
{"time":"2025-07-20T19:52:31.6586262+08:00","level":"INFO","msg":"handler: started","stream_id":"wo2r2j25"}
{"time":"2025-07-20T19:52:31.6586262+08:00","level":"INFO","msg":"sender: started","stream_id":"wo2r2j25"}
{"time":"2025-07-20T19:52:31.6586262+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wo2r2j25"}
{"time":"2025-07-20T19:52:47.2420513+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:02.2418966+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:17.2420025+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:32.2418176+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:47.2420038+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:02.2424313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:17.242616+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:32.2419575+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:47.2418761+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:55:02.2418479+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:55:17.2415822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:55:32.2420154+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:55:47.2423079+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:56:02.2419969+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:56:17.2419857+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:56:32.2422282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:56:47.2424553+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:02.2419755+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:17.2418787+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:32.2418679+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:47.2421334+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:02.2419586+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:17.2418036+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:32.2422066+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:47.2418334+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:02.2416587+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:17.2420333+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:32.2418398+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:47.2426845+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:02.2420354+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:17.2416907+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:32.2417003+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:47.2417776+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:02.241716+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:17.2417864+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:32.2420821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:47.241893+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:02.242092+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:17.2419237+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:32.2415703+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:47.2415668+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:02.241625+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:17.2416711+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:32.2419819+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:47.2415801+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:02.242365+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:17.2417979+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:32.2418111+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:47.2419356+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:02.2422794+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:17.2418137+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:32.2420209+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:47.2420737+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:02.2418637+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:17.2419932+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:32.2419731+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:20:02.2420459+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:20:17.2417678+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:20:32.2466707+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:20:47.2419461+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:21:02.241801+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:21:17.2420821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:21:32.2418567+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:21:47.241764+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:22:02.2416464+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:22:17.2415771+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:22:32.2421215+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:22:47.2416709+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:23:02.2417791+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:23:17.2417347+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:23:32.2446795+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:23:47.2417356+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:24:02.2422011+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:24:17.2418841+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:24:32.2417416+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:24:47.2422025+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:25:02.2427515+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:25:17.2416857+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:25:32.2417908+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:25:47.2418942+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:26:02.2416873+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:26:17.241792+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:26:32.241662+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:26:47.2419219+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:27:02.2640187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:27:17.2419982+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:27:32.2421907+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:27:47.2419642+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:28:02.2418434+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:28:17.2420819+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:28:32.241986+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:28:47.2416894+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:29:02.2418196+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:29:17.242235+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:29:32.2420303+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:29:47.2419736+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:30:02.2419555+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:30:17.242019+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:30:32.2416523+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:30:47.241842+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:31:02.2415417+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:31:17.2417796+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:31:32.2418446+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:31:47.2419946+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:32:02.2416548+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:32:17.2420023+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:32:32.2416266+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:32:47.2420362+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:33:02.2418993+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:33:17.2421009+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:33:32.2416535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:33:47.242108+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:34:02.2419187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:34:17.2418128+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:34:32.2420604+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:34:47.2417304+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:35:02.2420592+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:35:17.2418049+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:35:32.2419307+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:35:47.2414614+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:36:02.2419484+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:36:17.2418862+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:36:32.2417788+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:36:47.2420211+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:37:02.24159+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:37:17.241652+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:37:32.2419576+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:37:47.2419317+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:38:02.2419822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:38:17.2419851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:38:32.2418544+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:38:47.2416531+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:39:02.2418391+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:39:17.241945+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:39:32.2420013+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:39:47.2420336+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:40:02.2421455+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:40:17.2422361+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:40:32.2420383+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:40:47.2417901+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:41:02.2422206+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:41:17.2421744+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:41:32.2417221+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:41:47.241763+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:42:02.2416562+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:42:17.241909+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:42:32.2417063+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:42:47.2421897+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:43:02.2418096+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:43:17.2421486+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:43:32.2416304+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:43:47.2420468+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:44:02.2418404+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:44:17.2417048+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:44:32.2419406+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:44:47.2417215+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:45:02.2417754+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:45:17.2423515+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:45:32.2419509+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:45:47.2416765+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:53:47.8379185+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:53:47.8389177+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/willislipro-other/train/wo2r2j25/file_stream\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-20T20:54:02.2422677+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:54:17.2421203+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
