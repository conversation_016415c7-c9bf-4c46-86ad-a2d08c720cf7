{"time":"2025-07-20T19:52:29.9419568+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-20T19:52:31.6580852+08:00","level":"INFO","msg":"stream: created new stream","id":"wo2r2j25"}
{"time":"2025-07-20T19:52:31.6586262+08:00","level":"INFO","msg":"stream: started","id":"wo2r2j25"}
{"time":"2025-07-20T19:52:31.6586262+08:00","level":"INFO","msg":"handler: started","stream_id":"wo2r2j25"}
{"time":"2025-07-20T19:52:31.6586262+08:00","level":"INFO","msg":"sender: started","stream_id":"wo2r2j25"}
{"time":"2025-07-20T19:52:31.6586262+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wo2r2j25"}
{"time":"2025-07-20T19:52:47.2420513+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:02.2418966+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:17.2420025+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:32.2418176+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:53:47.2420038+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:02.2424313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:17.242616+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:32.2419575+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:54:47.2418761+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
