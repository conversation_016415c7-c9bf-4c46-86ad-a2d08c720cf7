import os
import shutil
import random

# 源目录
BASE = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
LABEL_SRC = os.path.join(BASE, 'labels')
IMG_SRC = os.path.join(BASE, 'images')

# 目标目录
DST = os.path.join(BASE, 'dataset_v1')
IMG_DST = os.path.join(DST, 'images')
LBL_DST = os.path.join(DST, 'labels')
DATASET_TXT = os.path.join(DST, 'dataSet')

for split in ['train', 'val', 'test']:
    os.makedirs(os.path.join(IMG_DST, split), exist_ok=True)
    os.makedirs(os.path.join(LBL_DST, split), exist_ok=True)
    os.makedirs(DATASET_TXT, exist_ok=True)

# 获取10个标签文件
label_files = [f for f in os.listdir(LABEL_SRC) if f.endswith('.txt') and f != 'classes.txt']
label_files.sort()
random.seed(42)
random.shuffle(label_files)

n = len(label_files)
train_num = int(n * 0.8)
val_num = int(n * 0.1)
train_files = label_files[:train_num]
val_files = label_files[train_num:train_num+val_num]
test_files = label_files[train_num+val_num:]

splits = [(train_files, 'train'), (val_files, 'val'), (test_files, 'test')]

for files, split in splits:
    txtlist = []
    for lbl in files:
        img = lbl.replace('.txt', '.jpg')
        src_img = os.path.join(IMG_SRC, img)
        src_lbl = os.path.join(LABEL_SRC, lbl)
        dst_img = os.path.join(IMG_DST, split, img)
        dst_lbl = os.path.join(LBL_DST, split, lbl)
        if os.path.exists(src_img) and os.path.exists(src_lbl):
            shutil.copy2(src_img, dst_img)
            shutil.copy2(src_lbl, dst_lbl)
            txtlist.append(lbl.replace('.txt', ''))
    # 生成图片名列表
    with open(os.path.join(DATASET_TXT, f'{split}.txt'), 'w') as f:
        for name in txtlist:
            f.write(name + '\n')
    print(f"{split}集: {len(txtlist)} 张")

print("划分与整理完成！") 