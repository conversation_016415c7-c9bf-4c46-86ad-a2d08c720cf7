#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
课后作业视频处理脚本
专门处理课后作业视频文件
"""

import os
import sys
import cv2
import numpy as np
import json
import csv
from pathlib import Path
from datetime import datetime
import time

# 添加YOLOv5路径
current_dir = Path(__file__).parent
project_dir = current_dir.parent
yolo_dir = project_dir / "相关源码和模型" / "VehicleCountor" / "yolov5"

if str(yolo_dir) not in sys.path:
    sys.path.append(str(yolo_dir))

from models.common import DetectMultiBackend
from utils.general import (LOGGER, colorstr, cv2, non_max_suppression, 
                          scale_coords, xyxy2xywh)
from utils.plots import Annotator, colors
from utils.torch_utils import select_device, time_sync


class HomeworkVideoProcessor:
    def __init__(self, weights, device='', conf_thres=0.25, iou_thres=0.45, max_det=1000):
        """初始化课后作业视频处理器"""
        self.device = select_device(device)
        self.model = DetectMultiBackend(weights, device=self.device)
        self.stride, self.names, self.pt = self.model.stride, self.model.names, self.model.pt
        self.conf_thres = conf_thres
        self.iou_thres = iou_thres
        self.max_det = max_det
        
        # 统计信息
        self.frame_count = 0
        self.total_objects = 0
        self.class_counts = {name: 0 for name in self.names}
        self.frame_statistics = []
        
        print(f"✅ 模型加载完成: {weights}")
        print(f"✅ 设备: {self.device}")
        print(f"✅ 类别: {self.names}")
        
    def process_homework_video(self, video_path, output_path=None, show_video=True, save_video=True):
        """处理课后作业视频"""
        print(f"🎬 开始处理课后作业视频: {video_path}")
        
        # 打开视频文件
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            print(f"❌ 无法打开视频文件: {video_path}")
            return None
        
        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"📹 视频信息:")
        print(f"  FPS: {fps}")
        print(f"  分辨率: {width}x{height}")
        print(f"  总帧数: {total_frames}")
        print(f"  时长: {total_frames/fps:.2f}秒")
        
        # 设置输出视频
        video_writer = None
        if save_video and output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
            print(f"💾 输出视频: {output_path}")
        
        # 模型预热
        self.model.warmup(imgsz=(1, 3, 640, 640))
        
        # 处理每一帧
        start_time = time.time()
        frame_times = []
        
        print(f"\n🚀 开始处理视频帧...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            self.frame_count += 1
            
            # 处理当前帧
            t1 = time_sync()
            processed_frame, frame_stats = self.process_frame(frame)
            t2 = time_sync()
            frame_time = t2 - t1
            frame_times.append(frame_time)
            
            # 显示进度
            if self.frame_count % 100 == 0:  # 每100帧显示一次进度
                progress = (self.frame_count / total_frames) * 100
                elapsed_time = time.time() - start_time
                avg_fps = self.frame_count / elapsed_time
                remaining_time = (total_frames - self.frame_count) / avg_fps if avg_fps > 0 else 0
                print(f"📊 进度: {progress:.1f}% ({self.frame_count}/{total_frames}) - FPS: {avg_fps:.1f} - 剩余时间: {remaining_time:.1f}秒")
            
            # 显示视频
            if show_video:
                # 缩放显示（如果分辨率太高）
                display_frame = processed_frame
                if width > 1920 or height > 1080:
                    scale = min(1920/width, 1080/height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    display_frame = cv2.resize(processed_frame, (new_width, new_height))
                
                cv2.imshow('YOLOv5 Homework Video Detection', display_frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):  # 按q退出
                    break
            
            # 保存视频
            if video_writer:
                video_writer.write(processed_frame)
            
            # 保存帧统计
            frame_stats['frame_number'] = self.frame_count
            frame_stats['processing_time'] = frame_time
            self.frame_statistics.append(frame_stats)
        
        # 清理资源
        cap.release()
        if video_writer:
            video_writer.release()
        if show_video:
            cv2.destroyAllWindows()
        
        # 计算处理统计
        total_time = time.time() - start_time
        avg_fps = self.frame_count / total_time
        avg_frame_time = np.mean(frame_times)
        
        print(f"\n🎉 课后作业视频处理完成!")
        print(f"📊 处理统计:")
        print(f"  总帧数: {self.frame_count}")
        print(f"  总目标数: {self.total_objects}")
        print(f"  处理时间: {total_time:.2f}秒")
        print(f"  平均FPS: {avg_fps:.1f}")
        print(f"  平均帧处理时间: {avg_frame_time*1000:.1f}ms")
        
        return {
            'total_frames': self.frame_count,
            'total_objects': self.total_objects,
            'class_counts': self.class_counts,
            'processing_time': total_time,
            'avg_fps': avg_fps,
            'frame_statistics': self.frame_statistics
        }
    
    def process_frame(self, frame):
        """处理单帧图像"""
        # 预处理
        im = cv2.resize(frame, (640, 640))
        im = im.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
        im = np.ascontiguousarray(im)
        im = np.expand_dims(im, axis=0)
        
        # 转换为tensor
        import torch
        im = torch.from_numpy(im).to(self.device)
        im = im.half() if self.model.fp16 else im.float()
        im /= 255.0
        
        # 推理
        pred = self.model(im, augment=False, visualize=False)
        
        # NMS
        pred = non_max_suppression(pred, self.conf_thres, self.iou_thres, 
                                 classes=None, max_det=self.max_det)
        
        # 处理检测结果
        frame_stats = {
            'objects_detected': 0,
            'class_detections': {name: 0 for name in self.names}
        }
        
        annotator = Annotator(frame, line_width=3, example=str(self.names))
        
        for det in pred:
            if len(det):
                # 缩放边界框到原始尺寸
                det[:, :4] = scale_coords(im.shape[2:], det[:, :4], frame.shape).round()
                
                # 统计检测结果
                for c in det[:, -1].unique():
                    n = (det[:, -1] == c).sum()
                    c_int = int(c)
                    class_name = self.names[c_int]
                    frame_stats['class_detections'][class_name] = n.item()
                    frame_stats['objects_detected'] += n.item()
                    self.class_counts[class_name] += n.item()
                    self.total_objects += n.item()
                
                # 绘制边界框
                for *xyxy, conf, cls in reversed(det):
                    c = int(cls)
                    label = f'{self.names[c]} {conf:.2f}'
                    annotator.box_label(xyxy, label, color=colors(c, True))
        
        # 在帧上显示统计信息
        self.draw_statistics(frame, frame_stats)
        
        return frame, frame_stats
    
    def draw_statistics(self, frame, frame_stats):
        """在帧上绘制统计信息"""
        # 绘制当前帧统计
        y_offset = 30
        cv2.putText(frame, f"Frame: {self.frame_count}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        y_offset += 25
        
        cv2.putText(frame, f"Objects: {frame_stats['objects_detected']}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        y_offset += 20
        
        # 绘制类别统计
        for class_name, count in frame_stats['class_detections'].items():
            if count > 0:
                cv2.putText(frame, f"{class_name}: {count}", (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                y_offset += 18
        
        # 绘制总统计
        y_offset += 10
        cv2.putText(frame, f"Total: {self.total_objects}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        y_offset += 25
        
        for class_name, count in self.class_counts.items():
            if count > 0:
                cv2.putText(frame, f"{class_name}: {count}", (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                y_offset += 18
    
    def save_homework_statistics(self, stats, output_dir):
        """保存课后作业视频处理统计"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存JSON统计
        json_path = output_dir / "homework_video_statistics.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        # 保存CSV帧级统计
        csv_path = output_dir / "homework_frame_statistics.csv"
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            if stats['frame_statistics']:
                fieldnames = ['frame_number', 'objects_detected', 'processing_time'] + list(self.names)
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for frame_stat in stats['frame_statistics']:
                    row = {
                        'frame_number': frame_stat['frame_number'],
                        'objects_detected': frame_stat['objects_detected'],
                        'processing_time': frame_stat['processing_time']
                    }
                    row.update(frame_stat['class_detections'])
                    writer.writerow(row)
        
        # 保存文本摘要
        txt_path = output_dir / "homework_video_summary.txt"
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("=== YOLOv5课后作业视频处理统计摘要 ===\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总帧数: {stats['total_frames']}\n")
            f.write(f"总目标数: {stats['total_objects']}\n")
            f.write(f"处理时间: {stats['processing_time']:.2f}秒\n")
            f.write(f"平均FPS: {stats['avg_fps']:.1f}\n")
            f.write(f"平均每帧目标数: {stats['total_objects']/stats['total_frames']:.2f}\n\n")
            
            f.write("各类别统计:\n")
            for class_name, count in stats['class_counts'].items():
                if count > 0:
                    percentage = (count / stats['total_objects']) * 100 if stats['total_objects'] > 0 else 0
                    f.write(f"  {class_name}: {count} ({percentage:.1f}%)\n")
        
        print(f"📄 课后作业统计报告已保存到: {output_dir}")
        return json_path, csv_path, txt_path


def main():
    """主函数"""
    print("🎬 YOLOv5课后作业视频处理系统")
    print("=" * 60)
    
    # 配置参数
    weights_path = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    video_path = project_dir / "课后作业视频" / "3007580-uhd_3840_2160_30fps.mp4"
    
    # 检查文件是否存在
    if not weights_path.exists():
        print(f"❌ 模型文件不存在: {weights_path}")
        print("请先完成模型训练！")
        return
    
    if not video_path.exists():
        print(f"❌ 课后作业视频文件不存在: {video_path}")
        return
    
    print(f"✅ 模型文件: {weights_path}")
    print(f"✅ 课后作业视频: {video_path}")
    print()
    
    # 设置输出路径
    output_video = video_path.parent / f"{video_path.stem}_detected{video_path.suffix}"
    
    # 创建处理器
    processor = HomeworkVideoProcessor(
        weights=str(weights_path),
        device='',  # 自动选择设备
        conf_thres=0.25,
        iou_thres=0.45,
        max_det=1000
    )
    
    print("开始处理课后作业视频...")
    print("-" * 40)
    
    # 处理视频
    stats = processor.process_homework_video(
        video_path=video_path,
        output_path=output_video,
        show_video=True,  # 显示处理过程
        save_video=True   # 保存处理结果
    )
    
    if stats:
        # 保存统计报告
        output_dir = video_path.parent / "homework_reports"
        processor.save_homework_statistics(stats, output_dir)
        
        print("\n" + "=" * 60)
        print("🎉 课后作业视频处理完成！")
        print("📁 输出文件:")
        print(f"  视频: {output_video}")
        print(f"  报告: {output_dir}")
        print("\n📊 处理统计:")
        print(f"  总帧数: {stats['total_frames']}")
        print(f"  总目标数: {stats['total_objects']}")
        print(f"  处理时间: {stats['processing_time']:.2f}秒")
        print(f"  平均FPS: {stats['avg_fps']:.1f}")
        print("\n各类别统计:")
        for class_name, count in stats['class_counts'].items():
            if count > 0:
                print(f"  {class_name}: {count}")


if __name__ == "__main__":
    main() 