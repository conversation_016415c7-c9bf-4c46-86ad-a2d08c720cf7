_wandb:
    value:
        cli_version: 0.21.0
        e:
            yjsemn3lhg06y1688baivsb0h0m1n7nj:
                args:
                    - --img
                    - "1024"
                    - --batch
                    - "4"
                    - --epochs
                    - "100"
                    - --data
                    - ../../../dataset_v1/dataset.yaml
                    - --weights
                    - yolov5n.pt
                    - --device
                    - "0"
                codePath: train.py
                codePathLocal: train.py
                cpu_count: 14
                cpu_count_logical: 20
                cudaVersion: "12.8"
                disk:
                    /:
                        total: "************"
                        used: "************"
                executable: C:\Users\<USER>\.conda\envs\yoloobb\python.exe
                gpu: NVIDIA GeForce RTX 3060 Laptop GPU
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 3840
                      memoryTotal: "6442450944"
                      name: NVIDIA GeForce RTX 3060 Laptop GPU
                      uuid: GPU-96e084a5-f3f5-ae24-8d91-e1f8c5adec6b
                host: deepmind
                memory:
                    total: "16962281472"
                os: Windows-10-10.0.26100-SP0
                program: train.py
                python: CPython 3.8.20
                root: D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5
                startedAt: "2025-07-18T18:06:08.380650Z"
                writerId: yjsemn3lhg06y1688baivsb0h0m1n7nj
        m: []
        python_version: 3.8.20
        t:
            "1":
                - 1
                - 41
            "2":
                - 1
                - 41
            "3":
                - 16
            "4": 3.8.20
            "5": 0.21.0
            "8":
                - 3
            "12": 0.21.0
            "13": windows-amd64
artifact_alias:
    value: latest
batch_size:
    value: 4
bbox_interval:
    value: -1
bucket:
    value: ""
cache:
    value: null
cfg:
    value: models/yolov5n.yaml
cos_lr:
    value: false
data:
    value: ../../../dataset_v1/dataset.yaml
device:
    value: "0"
entity:
    value: null
epochs:
    value: 100
evolve:
    value: null
exist_ok:
    value: false
freeze:
    value:
        - 0
hyp:
    value:
        anchor_t: 4
        box: 0.05
        cls: 0.5
        cls_pw: 1
        copy_paste: 0
        degrees: 0
        fl_gamma: 0
        fliplr: 0.5
        flipud: 0
        hsv_h: 0.015
        hsv_s: 0.7
        hsv_v: 0.4
        iou_t: 0.2
        lr0: 0.01
        lrf: 0.01
        mixup: 0
        momentum: 0.937
        mosaic: 1
        obj: 1
        obj_pw: 1
        perspective: 0
        scale: 0.5
        shear: 0
        translate: 0.1
        warmup_bias_lr: 0.1
        warmup_epochs: 3
        warmup_momentum: 0.8
        weight_decay: 0.0005
image_weights:
    value: false
imgsz:
    value: 1024
label_smoothing:
    value: 0
local_rank:
    value: -1
multi_scale:
    value: false
name:
    value: exp
noautoanchor:
    value: false
nosave:
    value: false
noval:
    value: false
optimizer:
    value: SGD
patience:
    value: 100
project:
    value: runs\train
quad:
    value: false
rect:
    value: false
resume:
    value: false
save_dir:
    value: runs\train\exp
save_period:
    value: -1
single_cls:
    value: false
sync_bn:
    value: false
upload_dataset:
    value: false
weights:
    value: yolov5n.pt
workers:
    value: 8
