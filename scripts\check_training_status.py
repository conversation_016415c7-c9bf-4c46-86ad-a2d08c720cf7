#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查YOLOv5x训练状态并更新Web界面
"""

import os
import time
from pathlib import Path

def check_training_status():
    """检查训练状态"""
    print("🔍 检查YOLOv5x训练状态...")
    print("=" * 50)
    
    # 训练目录
    train_dir = Path("相关源码和模型/VehicleCountor/yolov5/runs/train/vehicle_detection_yolov5x_v2")
    weights_dir = train_dir / "weights"
    
    if not train_dir.exists():
        print("❌ 训练目录不存在")
        return False
    
    print(f"✅ 训练目录存在: {train_dir}")
    
    # 检查权重文件
    best_weights = weights_dir / "best.pt"
    last_weights = weights_dir / "last.pt"
    
    if best_weights.exists():
        print(f"🏆 最佳权重已生成: {best_weights}")
        print(f"📊 文件大小: {best_weights.stat().st_size / (1024*1024):.1f} MB")
        return True
    elif last_weights.exists():
        print(f"📝 最新权重已生成: {last_weights}")
        print(f"📊 文件大小: {last_weights.stat().st_size / (1024*1024):.1f} MB")
        return True
    else:
        print("⏳ 训练进行中，权重文件尚未生成...")
        return False

def update_web_interface():
    """更新Web界面使用新的YOLOv5x模型"""
    print("\n🌐 更新Web界面配置...")
    
    web_interface_path = Path("scripts/web_interface.py")
    
    if not web_interface_path.exists():
        print(f"❌ Web界面文件不存在: {web_interface_path}")
        return
    
    # 读取Web界面文件
    with open(web_interface_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 新的模型路径
    new_model_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\runs\train\vehicle_detection_yolov5x_v2\weights\best.pt"
    
    # 检查best.pt是否存在，如果不存在则使用last.pt
    best_weights = Path("相关源码和模型/VehicleCountor/yolov5/runs/train/vehicle_detection_yolov5x_v2/weights/best.pt")
    if not best_weights.exists():
        new_model_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\runs\train\vehicle_detection_yolov5x_v2\weights\last.pt"
    
    # 替换模型路径
    old_model_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\runs\train\exp21\weights\last.pt"
    content = content.replace(old_model_path, new_model_path)
    
    # 更新模型名称
    content = content.replace("YOLOv5n", "YOLOv5x")
    content = content.replace("YOLOv5 Web界面系统", "YOLOv5x 高精度Web界面系统")
    
    # 写回文件
    with open(web_interface_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Web界面已更新为YOLOv5x!")

def main():
    """主函数"""
    print("🎯 YOLOv5x 训练状态检查工具")
    print("=" * 50)
    
    # 检查训练状态
    training_complete = check_training_status()
    
    if training_complete:
        # 更新Web界面
        update_web_interface()
        
        print("\n" + "=" * 50)
        print("🎉 YOLOv5x训练完成!")
        print("\n📋 下一步操作:")
        print("1. 启动Web界面: python scripts/web_interface.py")
        print("2. 访问地址: http://localhost:5000")
        
        print("\n💡 YOLOv5x优势:")
        print("- 更高的检测精度")
        print("- 更好的小目标检测能力")
        print("- 更强的特征提取能力")
        print("- 适合复杂场景")
    else:
        print("\n⏳ 训练仍在进行中...")
        print("💡 建议:")
        print("1. 等待训练完成")
        print("2. 定期运行此脚本检查状态")
        print("3. 训练完成后会自动更新Web界面")

if __name__ == "__main__":
    main() 