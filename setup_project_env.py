import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Runs a shell command and prints its output."""
    print(f"\n--- {description} ---")
    try:
        process = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(process.stdout)
        if process.stderr:
            print(process.stderr)
    except subprocess.CalledProcessError as e:
        print(f"Error: Command '{e.cmd}' failed with exit code {e.returncode}")
        print(f"Stdout: {e.stdout}")
        print(f"Stderr: {e.stderr}")
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        sys.exit(1)

def install_packages(packages, description):
    """Installs a list of Python packages using pip."""
    if not packages:
        print(f"No packages to install for: {description}")
        return
    package_list = " ".join(packages)
    run_command(f"{sys.executable} -m pip install {package_list}", f"Installing {description}")

def setup_environment():
    print("Starting environment setup...")

    # Check for CUDA availability
    cuda_available = False
    try:
        import torch
        if torch.cuda.is_available():
            cuda_available = True
            print(f"CUDA is available. PyTorch will be installed with GPU support (CUDA {torch.version.cuda}).")
        else:
            print("CUDA is not available. PyTorch will be installed with CPU support.")
    except ImportError:
        print("PyTorch not found. Will attempt to install with GPU support if CUDA is detected.")
        # Try to detect CUDA without torch being installed
        try:
            # Check for nvidia-smi on Linux/Windows
            if platform.system() == "Windows":
                subprocess.run("nvidia-smi", shell=True, check=True, capture_output=True)
            else: # Linux
                subprocess.run("nvidia-smi", shell=True, check=True, capture_output=True)
            cuda_available = True
            print("nvidia-smi detected. Assuming CUDA is available for GPU PyTorch installation.")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("nvidia-smi not found. Assuming CUDA is NOT available for GPU PyTorch installation.")

    # Install PyTorch and TorchVision
    pytorch_install_cmd = f"{sys.executable} -m pip install torch torchvision"
    if cuda_available:
        print("Attempting to install PyTorch and TorchVision with CUDA support...")
        # For Windows, often need to specify index-url for CUDA versions
        # This command might need adjustment based on exact CUDA version and PyTorch release
        # For example, for CUDA 11.8:
        # pytorch_install_cmd = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
        # For simplicity, we'll use the general command, which often works with newer pip
        pass # Use default command below
    else:
        print("Installing PyTorch and TorchVision with CPU support...")
        pytorch_install_cmd = f"{sys.executable} -m pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu"

    run_command(pytorch_install_cmd, "Installing PyTorch and TorchVision")

    # Install packages from requirements.txt
    # Filter out torch and torchvision as they are handled above
    # Correct path relative to traffic_counter_project
    requirements_path = os.path.join(os.path.dirname(__file__), '相关源码和模型', 'VehicleCountor', 'requirements.txt')

    if os.path.exists(requirements_path):
        with open(requirements_path, 'r') as f:
            lines = f.readlines()
        
        # Extract packages, ignoring comments and empty lines
        packages_from_req = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                package_name = line.split('==')[0].split('>=')[0].split('~=')[0].split('<')[0].split('>')[0]
                if package_name.lower() not in ['torch', 'torchvision']:
                    packages_from_req.append(line)
        
        install_packages(packages_from_req, "YOLOv5 and DeepSort dependencies from requirements.txt")
    else:
        print(f"Warning: requirements.txt not found at {requirements_path}. Skipping installation from requirements file.")

    # Install specific DeepSort dependencies if not already in requirements.txt
    # (gdown and torchreid are already in your requirements.txt, but keeping this for robustness)
    install_packages(['gdown', 'torchreid'], "Additional DeepSort dependencies")


    print("\nEnvironment setup complete. Please verify your PyTorch installation:")
    print("import torch")
    print("print(torch.cuda.is_available())")
    print("print(torch.version.cuda)")

if __name__ == "__main__":
    setup_environment()
