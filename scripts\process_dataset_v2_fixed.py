#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集V2处理脚本 - 修复版
处理新的车辆数据集，包括格式转换、数据集划分和配置生成
解决中文路径问题
"""

import os
import sys
import cv2
import numpy as np
import json
import xml.etree.ElementTree as ET
from pathlib import Path
import random
import shutil
from datetime import datetime

# 添加项目路径
current_dir = Path(__file__).parent
project_dir = current_dir.parent
dataset_dir = project_dir / "dataset_v2"

def analyze_dataset():
    """分析数据集结构"""
    print("🔍 分析数据集结构...")
    
    images_dir = dataset_dir / "images"
    xml_dir = dataset_dir / "xml"
    
    # 统计图片
    image_files = list(images_dir.glob("*.jpg"))
    print(f"📸 图片数量: {len(image_files)}")
    
    # 统计XML文件
    xml_files = list(xml_dir.glob("*.xml"))
    print(f"📄 XML文件数量: {len(xml_files)}")
    
    # 分析类别分布
    class_counts = {}
    total_objects = 0
    
    for xml_file in xml_files:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        for obj in root.findall('object'):
            name_elem = obj.find('name')
            if name_elem is not None:
                class_name = name_elem.text
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
                total_objects += 1
    
    print(f"🎯 总目标数: {total_objects}")
    print("📊 类别分布:")
    for class_name, count in class_counts.items():
        percentage = (count / total_objects) * 100
        print(f"  {class_name}: {count} ({percentage:.1f}%)")
    
    return class_counts, image_files, xml_files

def convert_voc_to_yolo(xml_file, image_width, image_height):
    """将VOC格式转换为YOLO格式"""
    tree = ET.parse(xml_file)
    root = tree.getroot()
    
    yolo_annotations = []
    
    for obj in root.findall('object'):
        name_elem = obj.find('name')
        if name_elem is None:
            continue
            
        class_name = name_elem.text
        
        # 类别映射
        class_mapping = {'car': 0, 'person': 1}
        class_id = class_mapping.get(class_name, -1)
        
        if class_id == -1:
            continue
        
        bbox = obj.find('bndbox')
        xmin = float(bbox.find('xmin').text)
        ymin = float(bbox.find('ymin').text)
        xmax = float(bbox.find('xmax').text)
        ymax = float(bbox.find('ymax').text)
        
        # 转换为YOLO格式 (x_center, y_center, width, height)
        x_center = (xmin + xmax) / 2.0 / image_width
        y_center = (ymin + ymax) / 2.0 / image_height
        width = (xmax - xmin) / image_width
        height = (ymax - ymin) / image_height
        
        yolo_annotations.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")
    
    return yolo_annotations

def get_image_size_safe(image_path):
    """安全地获取图片尺寸，处理中文路径问题"""
    try:
        # 方法1: 直接使用OpenCV
        img = cv2.imread(str(image_path))
        if img is not None:
            return img.shape[1], img.shape[0]  # width, height
        
        # 方法2: 使用PIL
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                return img.size  # (width, height)
        except ImportError:
            pass
        
        # 方法3: 从XML文件读取尺寸
        xml_file = image_path.parent.parent / "xml" / (image_path.stem + ".xml")
        if xml_file.exists():
            tree = ET.parse(xml_file)
            root = tree.getroot()
            size = root.find('size')
            if size is not None:
                width = int(size.find('width').text)
                height = int(size.find('height').text)
                return width, height
        
        return None, None
        
    except Exception as e:
        print(f"⚠️ 无法获取图片尺寸 {image_path}: {e}")
        return None, None

def process_dataset():
    """处理数据集"""
    print("🚀 开始处理数据集...")
    
    # 创建输出目录
    output_dir = dataset_dir / "yolo_format"
    output_dir.mkdir(exist_ok=True)
    
    # 创建子目录
    train_dir = output_dir / "train"
    val_dir = output_dir / "val"
    test_dir = output_dir / "test"
    
    for dir_path in [train_dir, val_dir, test_dir]:
        dir_path.mkdir(exist_ok=True)
        (dir_path / "images").mkdir(exist_ok=True)
        (dir_path / "labels").mkdir(exist_ok=True)
    
    # 获取所有图片和XML文件
    images_dir = dataset_dir / "images"
    xml_dir = dataset_dir / "xml"
    
    image_files = list(images_dir.glob("*.jpg"))
    xml_files = list(xml_dir.glob("*.xml"))
    
    # 随机打乱
    random.seed(42)
    combined_files = list(zip(image_files, xml_files))
    random.shuffle(combined_files)
    
    # 划分数据集 (70% 训练, 20% 验证, 10% 测试)
    total_files = len(combined_files)
    train_count = int(total_files * 0.7)
    val_count = int(total_files * 0.2)
    
    train_files = combined_files[:train_count]
    val_files = combined_files[train_count:train_count + val_count]
    test_files = combined_files[train_count + val_count:]
    
    print(f"📊 数据集划分:")
    print(f"  训练集: {len(train_files)} 张图片")
    print(f"  验证集: {len(val_files)} 张图片")
    print(f"  测试集: {len(test_files)} 张图片")
    
    # 处理每个数据集
    datasets = [
        (train_files, train_dir, "训练"),
        (val_files, val_dir, "验证"),
        (test_files, test_dir, "测试")
    ]
    
    total_objects = 0
    class_counts = {'car': 0, 'person': 0}
    processed_count = 0
    
    for files, output_path, dataset_name in datasets:
        print(f"\n🔄 处理{dataset_name}集...")
        
        for img_file, xml_file in files:
            # 获取图片尺寸
            width, height = get_image_size_safe(img_file)
            if width is None or height is None:
                print(f"⚠️ 跳过无法处理的图片: {img_file.name}")
                continue
            
            # 转换标注
            yolo_annotations = convert_voc_to_yolo(xml_file, width, height)
            
            # 统计目标
            for annotation in yolo_annotations:
                class_id = int(annotation.split()[0])
                class_name = 'car' if class_id == 0 else 'person'
                class_counts[class_name] += 1
                total_objects += 1
            
            # 复制图片
            dst_img = output_path / "images" / img_file.name
            try:
                shutil.copy2(img_file, dst_img)
                processed_count += 1
            except Exception as e:
                print(f"⚠️ 无法复制图片 {img_file.name}: {e}")
                continue
            
            # 保存标注
            dst_label = output_path / "labels" / (img_file.stem + ".txt")
            with open(dst_label, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_annotations))
    
    print(f"\n✅ 数据集处理完成!")
    print(f"📊 成功处理: {processed_count} 张图片")
    print(f"📊 总目标数: {total_objects}")
    print("📈 类别统计:")
    for class_name, count in class_counts.items():
        percentage = (count / total_objects) * 100 if total_objects > 0 else 0
        print(f"  {class_name}: {count} ({percentage:.1f}%)")
    
    return output_dir, class_counts

def create_dataset_yaml(output_dir, class_counts):
    """创建数据集配置文件"""
    print("\n📝 创建数据集配置文件...")
    
    # 获取绝对路径
    abs_output_dir = output_dir.absolute()
    
    yaml_content = f"""# 车辆检测数据集配置
# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# 数据集路径
path: {abs_output_dir}
train: train/images
val: val/images
test: test/images

# 类别数量和名称
nc: 2
names: ['car', 'person']

# 类别统计
# car: {class_counts.get('car', 0)}
# person: {class_counts.get('person', 0)}
"""
    
    yaml_path = output_dir / "dataset.yaml"
    with open(yaml_path, 'w', encoding='utf-8') as f:
        f.write(yaml_content)
    
    print(f"✅ 配置文件已保存: {yaml_path}")
    return yaml_path

def create_classes_file(output_dir):
    """创建类别文件"""
    classes_content = """car
person"""
    
    classes_path = output_dir / "classes.txt"
    with open(classes_path, 'w', encoding='utf-8') as f:
        f.write(classes_content)
    
    print(f"✅ 类别文件已保存: {classes_path}")

def create_training_script(output_dir, yaml_path):
    """创建训练脚本"""
    print("\n📝 创建训练脚本...")
    
    script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆检测训练脚本 - 数据集V2
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

from train import train

if __name__ == "__main__":
    # 训练参数
    data_yaml = r"{yaml_path.absolute()}"
    weights = "yolov5n.pt"  # 预训练权重
    epochs = 100
    batch_size = 16
    img_size = 640
    
    print("🚀 开始训练车辆检测模型...")
    print(f"📊 数据集: {{data_yaml}}")
    print(f"🎯 预训练权重: {{weights}}")
    print(f"⏱️ 训练轮数: {{epochs}}")
    print(f"📦 批次大小: {{batch_size}}")
    print(f"🖼️ 图片尺寸: {{img_size}}")
    print("-" * 50)
    
    # 开始训练
    train(
        data=data_yaml,
        weights=weights,
        epochs=epochs,
        batch_size=batch_size,
        imgsz=img_size,
        project="runs/train",
        name="vehicle_detection_v2",
        exist_ok=True,
        save_period=10,
        patience=20,
        device="0"  # 使用GPU
    )
    
    print("✅ 训练完成!")
'''
    
    script_path = output_dir / "train_vehicle_detection.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 训练脚本已保存: {script_path}")

def create_inference_script(output_dir):
    """创建推理脚本"""
    print("\n📝 创建推理脚本...")
    
    script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆检测推理脚本 - 数据集V2
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

from detect import run

if __name__ == "__main__":
    # 推理参数
    weights = "runs/train/vehicle_detection_v2/weights/best.pt"  # 训练好的模型
    source = "test_images"  # 测试图片目录
    conf_thres = 0.25
    iou_thres = 0.45
    
    print("🔍 开始车辆检测推理...")
    print(f"🎯 模型权重: {{weights}}")
    print(f"📸 测试图片: {{source}}")
    print(f"📊 置信度阈值: {{conf_thres}}")
    print(f"🔗 IoU阈值: {{iou_thres}}")
    print("-" * 50)
    
    # 开始推理
    run(
        weights=weights,
        source=source,
        conf_thres=conf_thres,
        iou_thres=iou_thres,
        project="runs/detect",
        name="vehicle_detection_v2",
        exist_ok=True,
        save_txt=True,
        save_conf=True
    )
    
    print("✅ 推理完成!")
'''
    
    script_path = output_dir / "inference_vehicle_detection.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 推理脚本已保存: {script_path}")

def main():
    """主函数"""
    print("🎯 车辆检测数据集V2处理系统 - 修复版")
    print("=" * 60)
    
    # 检查数据集目录
    if not dataset_dir.exists():
        print(f"❌ 数据集目录不存在: {dataset_dir}")
        return
    
    # 分析数据集
    class_counts, image_files, xml_files = analyze_dataset()
    
    if len(image_files) == 0:
        print("❌ 没有找到图片文件")
        return
    
    if len(xml_files) == 0:
        print("❌ 没有找到XML标注文件")
        return
    
    print()
    
    # 处理数据集
    output_dir, final_class_counts = process_dataset()
    
    # 创建配置文件
    yaml_path = create_dataset_yaml(output_dir, final_class_counts)
    
    # 创建类别文件
    create_classes_file(output_dir)
    
    # 创建训练脚本
    create_training_script(output_dir, yaml_path)
    
    # 创建推理脚本
    create_inference_script(output_dir)
    
    print("\n" + "=" * 60)
    print("🎉 数据集V2处理完成!")
    print("📁 输出目录:")
    print(f"  {output_dir}")
    print("\n📋 生成的文件:")
    print(f"  📊 数据集配置: {yaml_path}")
    print(f"  📝 类别文件: {output_dir}/classes.txt")
    print(f"  🚀 训练脚本: {output_dir}/train_vehicle_detection.py")
    print(f"  🔍 推理脚本: {output_dir}/inference_vehicle_detection.py")
    print("\n📊 数据集统计:")
    print(f"  训练集: {len(list((output_dir/'train'/'images').glob('*.jpg')))} 张图片")
    print(f"  验证集: {len(list((output_dir/'val'/'images').glob('*.jpg')))} 张图片")
    print(f"  测试集: {len(list((output_dir/'test'/'images').glob('*.jpg')))} 张图片")
    print("\n🎯 下一步:")
    print("  1. 运行训练脚本: python train_vehicle_detection.py")
    print("  2. 等待训练完成")
    print("  3. 运行推理脚本: python inference_vehicle_detection.py")

if __name__ == "__main__":
    main() 