#!/usr/bin/env python3
"""
测试数据集是否正确配置
"""
import os
from pathlib import Path

def test_dataset():
    print("🔍 测试数据集配置...")
    
    # 检查数据集文件
    train_file = "data/mydata/train.txt"
    val_file = "data/mydata/val.txt"
    
    print(f"\n📁 检查训练文件: {train_file}")
    if os.path.exists(train_file):
        with open(train_file, 'r') as f:
            train_paths = [line.strip() for line in f.readlines() if line.strip()]
        print(f"✅ 训练文件存在，包含 {len(train_paths)} 个图片路径")
        
        # 检查图片文件是否存在
        missing_images = []
        for img_path in train_paths:
            if not os.path.exists(img_path):
                missing_images.append(img_path)
        
        if missing_images:
            print(f"❌ 缺失的图片文件: {len(missing_images)}")
            for img in missing_images[:5]:  # 只显示前5个
                print(f"   - {img}")
        else:
            print("✅ 所有训练图片文件都存在")
            
        # 检查标签文件
        missing_labels = []
        for img_path in train_paths:
            label_path = img_path.replace('/images/', '/labels/').replace('.jpg', '.txt')
            if not os.path.exists(label_path):
                missing_labels.append(label_path)
        
        if missing_labels:
            print(f"❌ 缺失的标签文件: {len(missing_labels)}")
            for lbl in missing_labels[:5]:
                print(f"   - {lbl}")
        else:
            print("✅ 所有训练标签文件都存在")
    else:
        print(f"❌ 训练文件不存在: {train_file}")
    
    print(f"\n📁 检查验证文件: {val_file}")
    if os.path.exists(val_file):
        with open(val_file, 'r') as f:
            val_paths = [line.strip() for line in f.readlines() if line.strip()]
        print(f"✅ 验证文件存在，包含 {len(val_paths)} 个图片路径")
        
        # 检查图片文件是否存在
        missing_images = []
        for img_path in val_paths:
            if not os.path.exists(img_path):
                missing_images.append(img_path)
        
        if missing_images:
            print(f"❌ 缺失的图片文件: {len(missing_images)}")
            for img in missing_images[:5]:
                print(f"   - {img}")
        else:
            print("✅ 所有验证图片文件都存在")
            
        # 检查标签文件
        missing_labels = []
        for img_path in val_paths:
            label_path = img_path.replace('/images/', '/labels/').replace('.jpg', '.txt')
            if not os.path.exists(label_path):
                missing_labels.append(label_path)
        
        if missing_labels:
            print(f"❌ 缺失的标签文件: {len(missing_labels)}")
            for lbl in missing_labels[:5]:
                print(f"   - {lbl}")
        else:
            print("✅ 所有验证标签文件都存在")
    else:
        print(f"❌ 验证文件不存在: {val_file}")
    
    # 检查数据集配置文件
    config_file = "data/mydata.yaml"
    print(f"\n📁 检查配置文件: {config_file}")
    if os.path.exists(config_file):
        print("✅ 配置文件存在")
        with open(config_file, 'r') as f:
            print("📄 配置内容:")
            print(f.read())
    else:
        print(f"❌ 配置文件不存在: {config_file}")

if __name__ == "__main__":
    test_dataset()
