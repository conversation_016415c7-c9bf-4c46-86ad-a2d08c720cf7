# YOLOv5 Object Counter 🚀
# 基于YOLOv5的目标检测与计数脚本
# 支持图片、视频、目录批量处理

import argparse
import os
import sys
import json
import csv
from pathlib import Path
from datetime import datetime
import torch
import torch.backends.cudnn as cudnn
import cv2
import numpy as np

# 添加YOLOv5路径
YOLO_PATH = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
if str(YOLO_PATH) not in sys.path:
    sys.path.append(str(YOLO_PATH))

from models.common import DetectMultiBackend
from utils.datasets import IMG_FORMATS, VID_FORMATS, LoadImages, LoadStreams
from utils.general import (LOGGER, check_file, check_img_size, check_imshow, check_requirements, 
                          colorstr, cv2, increment_path, non_max_suppression, print_args, 
                          scale_coords, strip_optimizer, xyxy2xywh)
from utils.plots import Annotator, colors, save_one_box
from utils.torch_utils import select_device, time_sync


class ObjectCounter:
    def __init__(self, weights, device='', conf_thres=0.25, iou_thres=0.45, max_det=1000):
        """初始化目标计数器"""
        self.device = select_device(device)
        self.model = DetectMultiBackend(weights, device=self.device)
        self.stride, self.names, self.pt = self.model.stride, self.model.names, self.model.pt
        self.conf_thres = conf_thres
        self.iou_thres = iou_thres
        self.max_det = max_det
        
        # 统计信息
        self.total_counts = {name: 0 for name in self.names}
        self.frame_counts = []
        self.detection_history = []
        
    def count_objects(self, source, imgsz=(640, 640), save_results=True, project='runs/count', name='exp'):
        """执行目标检测和计数"""
        source = str(source)
        save_img = save_results and not source.endswith('.txt')
        
        # 创建保存目录
        save_dir = increment_path(Path(project) / name, exist_ok=True)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查输入类型
        is_file = Path(source).suffix[1:] in (IMG_FORMATS + VID_FORMATS)
        is_url = source.lower().startswith(('rtsp://', 'rtmp://', 'http://', 'https://'))
        webcam = source.isnumeric() or source.endswith('.txt') or (is_url and not is_file)
        
        if is_url and is_file:
            source = check_file(source)
            
        # 加载数据
        if webcam:
            view_img = check_imshow()
            cudnn.benchmark = True
            dataset = LoadStreams(source, img_size=imgsz, stride=self.stride, auto=self.pt)
            bs = len(dataset)
        else:
            dataset = LoadImages(source, img_size=imgsz, stride=self.stride, auto=self.pt)
            bs = 1
            
        vid_path, vid_writer = [None] * bs, [None] * bs
        
        # 模型预热
        self.model.warmup(imgsz=(1 if self.pt else bs, 3, *imgsz))
        
        # 处理每一帧/图片
        dt, seen = [0.0, 0.0, 0.0], 0
        for path, im, im0s, vid_cap, s in dataset:
            t1 = time_sync()
            
            # 预处理
            im = torch.from_numpy(im).to(self.device)
            im = im.half() if self.model.fp16 else im.float()
            im /= 255
            if len(im.shape) == 3:
                im = im[None]
            t2 = time_sync()
            dt[0] += t2 - t1
            
            # 推理
            pred = self.model(im, augment=False, visualize=False)
            t3 = time_sync()
            dt[1] += t3 - t2
            
            # NMS
            pred = non_max_suppression(pred, self.conf_thres, self.iou_thres, 
                                     classes=None, agnostic_nms=False, max_det=self.max_det)
            dt[2] += time_sync() - t3
            
            # 处理检测结果
            for i, det in enumerate(pred):
                seen += 1
                if webcam:
                    p, im0, frame = path[i], im0s[i].copy(), dataset.count
                    s += f'{i}: '
                else:
                    p, im0, frame = path, im0s.copy(), getattr(dataset, 'frame', 0)
                    
                p = Path(p)
                save_path = str(save_dir / p.name)
                s += '%gx%g ' % im.shape[2:]
                gn = torch.tensor(im0.shape)[[1, 0, 1, 0]]
                annotator = Annotator(im0, line_width=3, example=str(self.names))
                
                # 当前帧的检测结果
                frame_detections = {name: 0 for name in self.names}
                
                if len(det):
                    # 缩放边界框
                    det[:, :4] = scale_coords(im.shape[2:], det[:, :4], im0.shape).round()
                    
                    # 统计每个类别的数量
                    for c in det[:, -1].unique():
                        n = (det[:, -1] == c).sum()
                        c_int = int(c)
                        class_name = self.names[c_int]
                        frame_detections[class_name] = n.item()
                        self.total_counts[class_name] += n.item()
                        s += f"{n} {class_name}{'s' * (n > 1)}, "
                    
                    # 绘制边界框
                    for *xyxy, conf, cls in reversed(det):
                        c = int(cls)
                        label = f'{self.names[c]} {conf:.2f}'
                        annotator.box_label(xyxy, label, color=colors(c, True))
                
                # 保存当前帧的统计信息
                frame_info = {
                    'frame': seen,
                    'filename': p.name,
                    'path': str(p),
                    'detections': frame_detections,
                    'total_objects': sum(frame_detections.values())
                }
                self.frame_counts.append(frame_info)
                
                # 在图像上显示计数信息
                self.draw_count_info(im0, frame_detections, self.total_counts)
                
                # 保存结果
                if save_img:
                    if dataset.mode == 'image':
                        cv2.imwrite(save_path, im0)
                    else:
                        if vid_path[i] != save_path:
                            vid_path[i] = save_path
                            if isinstance(vid_writer[i], cv2.VideoWriter):
                                vid_writer[i].release()
                            if vid_cap:
                                fps = vid_cap.get(cv2.CAP_PROP_FPS)
                                w = int(vid_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                                h = int(vid_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                            else:
                                fps, w, h = 30, im0.shape[1], im0.shape[0]
                            save_path = str(Path(save_path).with_suffix('.mp4'))
                            vid_writer[i] = cv2.VideoWriter(save_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, (w, h))
                        vid_writer[i].write(im0)
                
                LOGGER.info(f'{s}Done. ({t3 - t2:.3f}s)')
        
        # 保存统计结果
        if save_results:
            self.save_statistics(save_dir)
            
        return self.total_counts, self.frame_counts
    
    def draw_count_info(self, im0, frame_detections, total_counts):
        """在图像上绘制计数信息"""
        # 绘制当前帧计数
        y_offset = 30
        cv2.putText(im0, f"Frame Count:", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        y_offset += 25
        
        for class_name, count in frame_detections.items():
            if count > 0:
                cv2.putText(im0, f"{class_name}: {count}", (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                y_offset += 20
        
        # 绘制总计数
        y_offset += 10
        cv2.putText(im0, f"Total Count:", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        y_offset += 25
        
        for class_name, count in total_counts.items():
            if count > 0:
                cv2.putText(im0, f"{class_name}: {count}", (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
                y_offset += 20
    
    def save_statistics(self, save_dir):
        """保存统计结果到文件"""
        # 保存JSON格式的详细统计
        stats = {
            'timestamp': datetime.now().isoformat(),
            'total_counts': self.total_counts,
            'frame_details': self.frame_counts,
            'summary': {
                'total_frames': len(self.frame_counts),
                'total_objects': sum(self.total_counts.values()),
                'average_objects_per_frame': sum(self.total_counts.values()) / len(self.frame_counts) if self.frame_counts else 0
            }
        }
        
        with open(save_dir / 'statistics.json', 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        # 保存CSV格式的帧级统计
        with open(save_dir / 'frame_statistics.csv', 'w', newline='', encoding='utf-8') as f:
            if self.frame_counts:
                fieldnames = ['frame', 'filename', 'path', 'total_objects'] + list(self.names)
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for frame_info in self.frame_counts:
                    row = {
                        'frame': frame_info['frame'],
                        'filename': frame_info['filename'],
                        'path': frame_info['path'],
                        'total_objects': frame_info['total_objects']
                    }
                    row.update(frame_info['detections'])
                    writer.writerow(row)
        
        # 保存总统计摘要
        with open(save_dir / 'summary.txt', 'w', encoding='utf-8') as f:
            f.write("=== 目标检测统计摘要 ===\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总帧数: {len(self.frame_counts)}\n")
            f.write(f"总目标数: {sum(self.total_counts.values())}\n")
            f.write(f"平均每帧目标数: {sum(self.total_counts.values()) / len(self.frame_counts) if self.frame_counts else 0:.2f}\n\n")
            
            f.write("各类别统计:\n")
            for class_name, count in self.total_counts.items():
                if count > 0:
                    percentage = (count / sum(self.total_counts.values())) * 100 if sum(self.total_counts.values()) > 0 else 0
                    f.write(f"  {class_name}: {count} ({percentage:.1f}%)\n")
        
        LOGGER.info(f"统计结果已保存到: {save_dir}")


def parse_opt():
    """解析命令行参数"""
    parser = argparse.ArgumentParser()
    parser.add_argument('--weights', type=str, default='yolov5n.pt', help='模型权重路径')
    parser.add_argument('--source', type=str, default='data/images', help='输入源 (图片/视频/目录)')
    parser.add_argument('--img-size', type=int, default=640, help='推理尺寸')
    parser.add_argument('--conf-thres', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou-thres', type=float, default=0.45, help='NMS IOU阈值')
    parser.add_argument('--max-det', type=int, default=1000, help='每张图片最大检测数')
    parser.add_argument('--device', default='', help='设备 (cuda device, i.e. 0 or 0,1,2,3 or cpu)')
    parser.add_argument('--project', default='runs/count', help='保存结果的项目目录')
    parser.add_argument('--name', default='exp', help='保存结果的实验名称')
    parser.add_argument('--nosave', action='store_true', help='不保存图片/视频')
    return parser.parse_args()


def main(opt):
    """主函数"""
    print(colorstr('bold', 'green') + 'YOLOv5 Object Counter' + colorstr('reset'))
    print_args(vars(opt))
    
    # 创建计数器
    counter = ObjectCounter(
        weights=opt.weights,
        device=opt.device,
        conf_thres=opt.conf_thres,
        iou_thres=opt.iou_thres,
        max_det=opt.max_det
    )
    
    # 执行计数
    total_counts, frame_counts = counter.count_objects(
        source=opt.source,
        imgsz=(opt.img_size, opt.img_size),
        save_results=not opt.nosave,
        project=opt.project,
        name=opt.name
    )
    
    # 打印结果
    print("\n" + "="*50)
    print("检测完成！统计结果:")
    print("="*50)
    for class_name, count in total_counts.items():
        if count > 0:
            print(f"{class_name}: {count}")
    print(f"总目标数: {sum(total_counts.values())}")
    print(f"处理帧数: {len(frame_counts)}")
    print("="*50)


if __name__ == "__main__":
    opt = parse_opt()
    main(opt) 