Dataset paths after resolution:
  train: D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\data\mydata\train.txt
  val: D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\data\mydata\val.txt
YOLOv5 temporarily requires wandb version 0.12.10 or below. Some features may not work as expected.
Overriding model.yaml nc=80 with nc=1

                 from  n    params  module                                  arguments
  0                -1  1      8800  models.common.Conv                      [3, 80, 6, 2, 2]
  1                -1  1    115520  models.common.Conv                      [80, 160, 3, 2]
  2                -1  4    309120  models.common.C3                        [160, 160, 4]
  3                -1  1    461440  models.common.Conv                      [160, 320, 3, 2]
  4                -1  8   2259200  models.common.C3                        [320, 320, 8]
  5                -1  1   1844480  models.common.Conv                      [320, 640, 3, 2]
  6                -1 12  13125120  models.common.C3                        [640, 640, 12]
  7                -1  1   7375360  models.common.Conv                      [640, 1280, 3, 2]
  8                -1  4  19676160  models.common.C3                        [1280, 1280, 4]
  9                -1  1   4099840  models.common.SPPF                      [1280, 1280, 5]
 10                -1  1    820480  models.common.Conv                      [1280, 640, 1, 1]
 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
 12           [-1, 6]  1         0  models.common.Concat                    [1]
 13                -1  4   5332480  models.common.C3                        [1280, 640, 4, False]
 14                -1  1    205440  models.common.Conv                      [640, 320, 1, 1]
 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']
 16           [-1, 4]  1         0  models.common.Concat                    [1]
 17                -1  4   1335040  models.common.C3                        [640, 320, 4, False]
 18                -1  1    922240  models.common.Conv                      [320, 320, 3, 2]
 19          [-1, 14]  1         0  models.common.Concat                    [1]
 20                -1  4   4922880  models.common.C3                        [640, 640, 4, False]
 21                -1  1   3687680  models.common.Conv                      [640, 640, 3, 2]
 22          [-1, 10]  1         0  models.common.Concat                    [1]
 23                -1  4  19676160  models.common.C3                        [1280, 1280, 4, False]
 24      [17, 20, 23]  1     40374  models.yolo.Detect                      [1, [[10, 13, 16, 30, 33, 23], [30, 61, 62, 45, 59, 119], [116, 90, 156, 198, 373, 326]], [320, 640, 1280]]
YOLOv5x summary: 567 layers, 86217814 parameters, 86217814 gradients, 204.6 GFLOPs

Transferred 738/745 items from yolov5x.pt
Scaled weight_decay = 0.0005
[34m[1moptimizer:[0m SGD with parameter groups 123 weight (no decay), 126 weight, 126 bias
D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\utils\augmentations.py:31: UserWarning: Argument(s) 'quality_lower' are not valid for transform ImageCompression
  A.ImageCompression(quality_lower=75, p=0.0)]  # transforms
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\albumentations\core\composition.py:331: UserWarning: Got processor for bboxes, but no transform to process it.
  self._set_keys()
[34m[1malbumentations: [0mBlur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
[34m[1mtrain: [0mScanning 'D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\data[0m
[34m[1mtrain: [0mWARNING: C:\Users\<USER>\Desktop\Lessons\VehicleCountor\yolov5\data\mydata\images\1.jpg: ignoring corrupt image/label: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Lessons\\VehicleCountor\\yolov5\\data\\mydata\\images\\1.jpg'
[34m[1mtrain: [0mWARNING: C:\Users\<USER>\Desktop\Lessons\VehicleCountor\yolov5\data\mydata\images\10.jpg: ignoring corrupt image/label: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Lessons\\VehicleCountor\\yolov5\\data\\mydata\\images\\10.jpg'
[34m[1mtrain: [0mWARNING: C:\Users\<USER>\Desktop\Lessons\VehicleCountor\yolov5\data\mydata\images\15.jpg: ignoring corrupt image/label: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Lessons\\VehicleCountor\\yolov5\\data\\mydata\\images\\15.jpg'
[34m[1mtrain: [0mWARNING: C:\Users\<USER>\Desktop\Lessons\VehicleCountor\yolov5\data\mydata\images\2.jpg: ignoring corrupt image/label: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Lessons\\VehicleCountor\\yolov5\\data\\mydata\\images\\2.jpg'
[34m[1mtrain: [0mWARNING: C:\Users\<USER>\Desktop\Lessons\VehicleCountor\yolov5\data\mydata\images\5.jpg: ignoring corrupt image/label: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Lessons\\VehicleCountor\\yolov5\\data\\mydata\\images\\5.jpg'
[34m[1mtrain: [0mWARNING: C:\Users\<USER>\Desktop\Lessons\VehicleCountor\yolov5\data\mydata\images\7.jpg: ignoring corrupt image/label: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Lessons\\VehicleCountor\\yolov5\\data\\mydata\\images\\7.jpg'
[34m[1mtrain: [0mWARNING: No labels found in D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\data\mydata\train.cache. See https://github.com/ultralytics/yolov5/wiki/Train-Custom-Data
[34m[1mtrain: [0mWARNING: Cache directory D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\data\mydata is not writeable: [WinError 183] 当文件已存在时，无法创建该文件。: 'D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\data\\mydata\\train.cache.npy' -> 'D:\\大三下\\智能信息处理技术\\基于YOLO-OBB算法的交通智能体目标识别算法\\lkr_yolo\\traffic_counter_project\\相关源码和模型\\VehicleCountor\\yolov5\\data\\mydata\\train.cache'
Traceback (most recent call last):
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\train_simple.py", line 72, in <module>
    train.main(opt)
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\train.py", line 562, in main
    train(opt.hyp, opt, device, callbacks)
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\train.py", line 221, in train
    train_loader, dataset = create_dataloader(train_path,
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\utils\datasets.py", line 116, in create_dataloader
    dataset = LoadImagesAndLabels(
              ^^^^^^^^^^^^^^^^^^^^
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\utils\datasets.py", line 463, in __init__
    assert nf > 0 or not augment, f'{prefix}No labels in {cache_path}. Can not train without labels. See {HELP_URL}'
           ^^^^^^^^^^^^^^^^^^^^^
AssertionError: [34m[1mtrain: [0mNo labels in D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\data\mydata\train.cache. Can not train without labels. See https://github.com/ultralytics/yolov5/wiki/Train-Custom-Data
