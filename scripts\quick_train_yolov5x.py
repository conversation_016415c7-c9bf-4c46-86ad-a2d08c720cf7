#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速YOLOv5x训练脚本
跳过验证和依赖检查，专注于训练
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🚀 快速YOLOv5x训练系统")
    print("=" * 60)
    
    # 项目路径
    project_dir = Path(__file__).parent.parent
    yolo_dir = project_dir / "相关源码和模型" / "VehicleCountor" / "yolov5"
    dataset_yaml = project_dir / "simple_dataset" / "dataset.yaml"
    weights_path = project_dir / "models" / "yolov5x.pt"
    
    # 检查文件是否存在
    if not weights_path.exists():
        print(f"❌ YOLOv5x权重文件不存在: {weights_path}")
        return
    
    if not dataset_yaml.exists():
        print(f"❌ 数据集配置文件不存在: {dataset_yaml}")
        return
    
    print(f"✅ 找到YOLOv5x权重: {weights_path}")
    print(f"✅ 找到数据集配置: {dataset_yaml}")
    
    # 训练参数
    epochs = 1000
    batch_size =2 
    img_size = 1024
    patience = 200
    
    print(f"\n📊 训练参数:")
    print(f"  训练轮数: {epochs}")
    print(f"  批次大小: {batch_size}")
    print(f"  图片尺寸: {img_size}x{img_size}")
    print(f"  早停耐心: {patience}")
    
    # 切换到YOLOv5目录
    os.chdir(yolo_dir)
    
    # 构建训练命令 - 跳过验证和依赖检查
    cmd = f"""python train.py --data "{dataset_yaml}" --weights "{weights_path}" --epochs {epochs} --batch-size {batch_size} --img {img_size} --project runs/train --name vehicle_detection_yolov5x --exist-ok --device 0 --patience {patience} --save-period 10 --noval --noautoanchor --nosave"""
    
    print(f"\n🔧 执行训练命令:")
    print(cmd)
    print("\n" + "=" * 60)
    
    # 询问是否开始训练
    response = input("\n是否开始YOLOv5x训练? (y/n): ").lower().strip()
    
    if response == 'y':
        try:
            # 设置环境变量禁用wandb
            env = os.environ.copy()
            env['WANDB_MODE'] = 'disabled'
            
            # 执行训练
            result = subprocess.run(cmd, shell=True, check=True, env=env)
            print("\n✅ YOLOv5x训练完成!")
            
            # 显示训练结果
            train_dir = yolo_dir / "runs" / "train" / "vehicle_detection_yolov5x"
            if train_dir.exists():
                print(f"\n📁 训练结果保存在: {train_dir}")
                
                # 检查最佳权重
                best_weights = train_dir / "weights" / "best.pt"
                last_weights = train_dir / "weights" / "last.pt"
                
                if best_weights.exists():
                    print(f"🏆 最佳权重: {best_weights}")
                if last_weights.exists():
                    print(f"📝 最新权重: {last_weights}")
            
            print("\n" + "=" * 60)
            print("🎉 YOLOv5x训练完成!")
            print("\n📋 下一步操作:")
            print("1. 启动Web界面: python web_interface.py")
            print("2. 访问地址: http://localhost:5000")
            
            print("\n💡 YOLOv5x优势:")
            print("- 更高的检测精度")
            print("- 更好的小目标检测能力")
            print("- 更强的特征提取能力")
            print("- 适合复杂场景")
            
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 训练失败: {e}")
            print("\n💡 建议:")
            print("1. 检查GPU内存是否足够")
            print("2. 尝试减小batch_size")
            print("3. 尝试减小img_size")
        except KeyboardInterrupt:
            print("\n⚠️ 训练被用户中断")
    else:
        print("\n⚠️ 训练已取消")

if __name__ == "__main__":
    main() 