# -*- coding: utf-8 -*-
import xml.etree.ElementTree as ET
import os
from os import getcwd
import argparse

sets = ['train', 'val', 'test']
classes = ["car", "person", "tricycle"]  # 改成自己的类别

# 通过命令行参数接收数据集的主目录，彻底解决路径问题
parser = argparse.ArgumentParser(description='Convert VOC XML annotations to YOLO format.')
parser.add_argument(
    '--dataset-dir',
    type=str,
    required=True,
    help='The absolute path to the dataset directory (e.g., .../dataset_v1)'
)
args = parser.parse_args()
# 关键修复：自动剥离命令行可能传入的多余引号，彻底解决路径语法错误
BASE_DIR = args.dataset_dir.strip('"\'')

XML_DIR = os.path.join(BASE_DIR, 'xml')
LABELS_DIR = os.path.join(BASE_DIR, 'labels')
DATASET_TXT = os.path.join(BASE_DIR, 'dataSet')

if not os.path.exists(LABELS_DIR):
    os.makedirs(LABELS_DIR)

print(f"XML目录: {XML_DIR}")
print(f"标签输出目录: {LABELS_DIR}")
print(f"图片名列表目录: {DATASET_TXT}")

def convert(size, box):
    dw = 1. / (size[0])
    dh = 1. / (size[1])
    x = (box[0] + box[1]) / 2.0 - 1
    y = (box[2] + box[3]) / 2.0 - 1
    w = box[1] - box[0]
    h = box[3] - box[2]
    x = x * dw
    w = w * dw
    y = y * dh
    h = h * dh
    return x, y, w, h

def convert_annotation(image_id):
    in_file = open(os.path.join(XML_DIR, '%s.xml' % (image_id)), encoding='UTF-8')
    out_file = open(os.path.join(LABELS_DIR, '%s.txt' % (image_id)), 'w')
    tree = ET.parse(in_file)
    root = tree.getroot()
    size = root.find('size')
    w = int(size.find('width').text)
    h = int(size.find('height').text)
    for obj in root.iter('object'):
        difficult = obj.find('difficult').text
        cls = obj.find('name').text
        if cls not in classes or int(difficult) == 1:
            continue
        cls_id = classes.index(cls)
        xmlbox = obj.find('bndbox')
        b = (float(xmlbox.find('xmin').text), float(xmlbox.find('xmax').text), float(xmlbox.find('ymin').text),
             float(xmlbox.find('ymax').text))
        b1, b2, b3, b4 = b
        # 标注越界修正
        if b2 > w:
            b2 = w
        if b4 > h:
            b4 = h
        b = (b1, b2, b3, b4)
        bb = convert((w, h), b)
        out_file.write(str(cls_id) + " " + " ".join([str(a) for a in bb]) + '\n')

# 直接遍历XML目录下的所有xml文件
xml_files = [f for f in os.listdir(XML_DIR) if f.endswith('.xml')]
if not xml_files:
    print(f"在目录 {XML_DIR} 中未找到任何 .xml 文件。")
else:
    for xml_file in xml_files:
        image_id = os.path.splitext(xml_file)[0]
        print(f"转换: {image_id}")
        try:
            convert_annotation(image_id)
        except Exception as e:
            print(f"处理文件 {xml_file} 时出错: {e}")

print("\n转换完成。")