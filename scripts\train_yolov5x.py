#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用YOLOv5x训练车辆检测模型
提升检测精度
"""

import os
import sys
import subprocess
from pathlib import Path

def train_yolov5x():
    """使用YOLOv5x训练模型"""
    print("🚀 YOLOv5x 高精度训练系统")
    print("=" * 60)
    
    # 项目路径
    project_dir = Path(__file__).parent.parent
    yolo_dir = project_dir / "相关源码和模型" / "VehicleCountor" / "yolov5"
    dataset_yaml = project_dir / "simple_dataset" / "dataset.yaml"
    weights_path = project_dir / "models" / "yolov5x.pt"
    
    # 检查文件是否存在
    if not weights_path.exists():
        print(f"❌ YOLOv5x权重文件不存在: {weights_path}")
        return
    
    if not dataset_yaml.exists():
        print(f"❌ 数据集配置文件不存在: {dataset_yaml}")
        return
    
    print(f"✅ 找到YOLOv5x权重: {weights_path}")
    print(f"✅ 找到数据集配置: {dataset_yaml}")
    
    # 训练参数
    epochs = 1000  # 增加训练轮数
    batch_size = 4  # 减小批次大小以适应更大的模型
    img_size = 1024
    device = 0  # GPU
    patience = 200
    print(f"\n📊 训练参数:")
    print(f"  训练轮数: {epochs}")
    print(f"  批次大小: {batch_size}")
    print(f"  图片尺寸: {img_size}x{img_size}")
    print(f"  设备: GPU {device}")
    
    # 构建训练命令
    cmd = [
        "python", "train.py",
        "--data", str(dataset_yaml),
        "--weights", str(weights_path),
        "--epochs", str(epochs),
        "--batch-size", str(batch_size),
        "--img", str(img_size),
        "--project", "runs/train",
        "--name", "vehicle_detection_yolov5x",
        "--exist-ok",
        "--device", str(device),
        "--patience", str(patience),  # 早停耐心值
        "--save-period", "10",  # 每10轮保存一次
        "--cache", "ram",  # 缓存数据到内存
        "--noval",  # 跳过验证以避免数据集路径问题
        "--nosave",  # 不保存中间结果
        "--noautoanchor"  # 禁用自动锚点
    ]
    
    print(f"\n🔧 执行训练命令:")
    print(" ".join(cmd))
    print("\n" + "=" * 60)
    
    # 切换到YOLOv5目录
    os.chdir(yolo_dir)
    
    try:
        # 执行训练
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ YOLOv5x训练完成!")
        
        # 显示训练结果
        train_dir = yolo_dir / "runs" / "train" / "vehicle_detection_yolov5x"
        if train_dir.exists():
            print(f"\n📁 训练结果保存在: {train_dir}")
            
            # 检查最佳权重
            best_weights = train_dir / "weights" / "best.pt"
            last_weights = train_dir / "weights" / "last.pt"
            
            if best_weights.exists():
                print(f"🏆 最佳权重: {best_weights}")
            if last_weights.exists():
                print(f"📝 最新权重: {last_weights}")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
        return False
    
    return True

def create_inference_script():
    """创建推理脚本"""
    print("\n📝 创建YOLOv5x推理脚本...")
    
    inference_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv5x 车辆检测推理脚本
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

def run_inference():
    """运行推理"""
    # 训练好的模型路径
    weights = "runs/train/vehicle_detection_yolov5x/weights/best.pt"
    
    # 测试图片
    source = "../../../simple_dataset/val/images"
    
    # 推理参数
    conf_thres = 0.25
    iou_thres = 0.45
    
    print("🔍 YOLOv5x 车辆检测推理")
    print("=" * 40)
    print(f"🎯 模型: {weights}")
    print(f"📸 测试图片: {source}")
    print(f"📊 置信度阈值: {conf_thres}")
    print(f"🔗 IoU阈值: {iou_thres}")
    
    # 构建推理命令
    cmd = [
        "python", "detect.py",
        "--weights", weights,
        "--source", source,
        "--conf", str(conf_thres),
        "--iou", str(iou_thres),
        "--project", "runs/detect",
        "--name", "yolov5x_inference",
        "--exist-ok",
        "--save-txt",
        "--save-conf"
    ]
    
    # 切换到YOLOv5目录
    os.chdir(yolo_dir)
    
    # 执行推理
    os.system(" ".join(cmd))
    print("✅ 推理完成!")

if __name__ == "__main__":
    run_inference()
'''
    
    script_path = Path(__file__).parent / "inference_yolov5x.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(inference_script)
    
    print(f"✅ 推理脚本已创建: {script_path}")

def update_web_interface_yolov5x():
    """更新Web界面使用YOLOv5x模型"""
    print("\n🌐 更新Web界面配置...")
    
    web_interface_path = Path(__file__).parent / "web_interface.py"
    
    if not web_interface_path.exists():
        print(f"❌ Web界面文件不存在: {web_interface_path}")
        return
    
    # 读取Web界面文件
    with open(web_interface_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 新的模型路径
    new_model_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\runs\train\vehicle_detection_yolov5x\weights\best.pt"
    
    # 替换模型路径
    old_model_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\runs\train\exp21\weights\last.pt"
    content = content.replace(old_model_path, new_model_path)
    
    # 更新模型名称
    content = content.replace("YOLOv5n", "YOLOv5x")
    content = content.replace("YOLOv5 Web界面系统", "YOLOv5x 高精度Web界面系统")
    
    # 写回文件
    with open(web_interface_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Web界面已更新为YOLOv5x!")

def main():
    """主函数"""
    print("🎯 YOLOv5x 高精度车辆检测训练系统")
    print("=" * 60)
    
    # 询问是否开始训练
    response = input("\n是否开始YOLOv5x训练? (y/n): ").lower().strip()
    
    if response == 'y':
        # 开始训练
        success = train_yolov5x()
        
        if success:
            # 创建推理脚本
            create_inference_script()
            
            # 更新Web界面
            update_web_interface_yolov5x()
            
            print("\n" + "=" * 60)
            print("🎉 YOLOv5x训练完成!")
            print("\n📋 下一步操作:")
            print("1. 运行推理测试: python inference_yolov5x.py")
            print("2. 启动Web界面: python web_interface.py")
            print("3. 访问地址: http://localhost:5000")
            
            print("\n💡 YOLOv5x优势:")
            print("- 更高的检测精度")
            print("- 更好的小目标检测能力")
            print("- 更强的特征提取能力")
            print("- 适合复杂场景")
        else:
            print("\n❌ 训练失败，请检查错误信息")
    else:
        print("\n⚠️ 训练已取消")

if __name__ == "__main__":
    main() 