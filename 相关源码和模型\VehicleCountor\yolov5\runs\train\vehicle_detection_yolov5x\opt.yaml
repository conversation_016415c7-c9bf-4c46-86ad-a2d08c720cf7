weights: ../../../models/yolov5x.pt
cfg: models/yolov5n.yaml
data: ../../../simple_dataset/dataset.yaml
hyp: data\hyps\hyp.scratch-low.yaml
epochs: 1000
batch_size: 2
imgsz: 1024
rect: false
resume: false
nosave: true
noval: true
noautoanchor: true
evolve: null
bucket: ''
cache: null
image_weights: false
device: '0'
multi_scale: false
single_cls: false
optimizer: SGD
sync_bn: false
workers: 8
project: runs/train
name: vehicle_detection_yolov5x
exist_ok: true
quad: false
cos_lr: false
label_smoothing: 0.0
patience: 200
freeze:
- 0
save_period: 10
local_rank: -1
entity: null
upload_dataset: false
bbox_interval: -1
artifact_alias: latest
save_dir: runs\train\vehicle_detection_yolov5x
