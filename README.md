# 高速车流量计数器项目

## 项目结构
```
traffic_counter_project/
├── data/           # 原始数据（视频文件）
├── images/         # 抽帧图片（用于标注）
├── labels/         # 标注文件（xml格式）
├── scripts/        # Python脚本
├── models/         # 训练好的模型
└── README.md       # 项目说明
```

## 使用步骤

### 1. 抽帧
```bash
cd scripts
python extract_frames.py
```

### 2. 标注
使用 labelImg 对 images/ 中的图片进行标注，保存为 VOC 格式（xml）

### 3. 格式转换
将 xml 标注文件转换为 YOLO 格式（txt）

### 4. 训练
使用 YOLOv5 训练车辆检测模型

### 5. 推理与计数
使用 YOLOv5 + DeepSort 实现车辆检测和计数

### 6. 前端可视化
使用 gradio 创建 Web 界面

## 文件说明
- `data/3007580-uhd_3840_2160_30fps.mp4`: 原始测试视频
- `images/frame_*.jpg`: 抽帧图片（30张）
- `scripts/extract_frames.py`: 抽帧脚本 