class C3CA(nn.Module):
    # C3 module with CoordAtt attention
    def __init__(self, c1, c2, n=1, shortcut=True, g=1, e=0.5):  # ch_in, ch_out, number, shortcut, groups, expansion
        super().__init__()
        c_ = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c1, c_, 1, 1)
        self.cv3 = Conv(2 * c_, c2, 1)  # act=FReLU(c2)
        self.m = nn.Sequential(*(Bottleneck(c_, c_, shortcut, g, e=1.0) for _ in range(n)))
        self.ca = CoordAtt(c2, c2)

    def forward(self, x):
        return self.ca(self.cv3(torch.cat((self.m(self.cv1(x)), self.cv2(x)), dim=1)))