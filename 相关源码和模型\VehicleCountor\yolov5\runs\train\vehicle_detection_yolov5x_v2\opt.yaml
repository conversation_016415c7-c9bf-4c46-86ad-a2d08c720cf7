weights: "D:\\\u5927\u4E09\u4E0B\\\u667A\u80FD\u4FE1\u606F\u5904\u7406\u6280\u672F\
  \\\u57FA\u4E8EYOLO-OBB\u7B97\u6CD5\u7684\u4EA4\u901A\u667A\u80FD\u4F53\u76EE\u6807\
  \u8BC6\u522B\u7B97\u6CD5\\lkr_yolo\\traffic_counter_project\\models\\yolov5x.pt"
cfg: models/yolov5n.yaml
data: "D:\\\u5927\u4E09\u4E0B\\\u667A\u80FD\u4FE1\u606F\u5904\u7406\u6280\u672F\\\u57FA\
  \u4E8EYOLO-OBB\u7B97\u6CD5\u7684\u4EA4\u901A\u667A\u80FD\u4F53\u76EE\u6807\u8BC6\
  \u522B\u7B97\u6CD5\\lkr_yolo\\traffic_counter_project\\dataset_v2\\yolo_format\\\
  dataset.yaml"
hyp: data\hyps\hyp.scratch-low.yaml
epochs: 1000
batch_size: 2
imgsz: 1024
rect: false
resume: false
nosave: false
noval: false
noautoanchor: false
evolve: null
bucket: ''
cache: null
image_weights: false
device: '0'
multi_scale: false
single_cls: false
optimizer: SGD
sync_bn: false
workers: 8
project: runs/train
name: vehicle_detection_yolov5x_v2
exist_ok: true
quad: false
cos_lr: false
label_smoothing: 0.0
patience: 200
freeze:
- 0
save_period: 10
local_rank: -1
entity: null
upload_dataset: false
bbox_interval: -1
artifact_alias: latest
save_dir: runs\train\vehicle_detection_yolov5x_v2
