# 车辆检测数据集V2总结

## 数据集信息
- 总图片数: 15张
- 类别: car, person
- 标注格式: VOC XML -> YOLO TXT

## 数据集分布
- 训练集: 12张图片 (80%)
- 验证集: 3张图片 (20%)

## 文件结构
```
dataset_v2/
├── images/          # 原始图片
├── xml/            # VOC格式标注
└── yolo_format/    # YOLO格式数据集

simple_dataset/
├── train/
│   ├── images/     # 训练图片
│   └── labels/     # YOLO格式标注
├── val/
│   ├── images/     # 验证图片
│   └── labels/     # YOLO格式标注
└── dataset.yaml    # 数据集配置
```

## 训练模型
- 模型路径: runs/train/exp21/weights/last.pt
- 训练轮数: 100
- 批次大小: 16
- 图片尺寸: 640x640

## 使用说明
1. 运行Web界面: python web_interface.py
2. 测试模型: python test_vehicle_detection.py
3. 访问地址: http://localhost:5000

## 注意事项
- 数据集较小，建议增加更多标注数据
- 可以考虑数据增强来提高模型性能
- 建议使用更大的模型（如yolov5s, yolov5m）来提升检测精度
