#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的视频处理运行脚本
"""

import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
project_dir = current_dir.parent
yolo_dir = project_dir / "相关源码和模型" / "VehicleCountor" / "yolov5"

# 添加YOLOv5路径到系统路径
if str(yolo_dir) not in sys.path:
    sys.path.append(str(yolo_dir))

# 导入视频处理器
from video_processor import VideoProcessor


def process_example_video():
    """处理示例视频"""
    print("🎬 YOLOv5视频处理系统")
    print("=" * 50)
    
    # 配置参数
    weights_path = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    video_path = yolo_dir / "example.mp4"
    
    # 检查文件是否存在
    if not weights_path.exists():
        print(f"❌ 模型文件不存在: {weights_path}")
        print("请先完成模型训练！")
        return
    
    if not video_path.exists():
        print(f"❌ 视频文件不存在: {video_path}")
        print("请将视频文件放在 yolov5/example.mp4")
        return
    
    print(f"✅ 模型文件: {weights_path}")
    print(f"✅ 视频文件: {video_path}")
    print()
    
    # 创建视频处理器
    processor = VideoProcessor(
        weights=str(weights_path),
        device='',  # 自动选择设备
        conf_thres=0.25,
        iou_thres=0.45,
        max_det=1000
    )
    
    # 设置输出路径
    output_video = video_path.parent / f"{video_path.stem}_detected{video_path.suffix}"
    
    print("开始视频处理...")
    print("-" * 30)
    
    # 处理视频
    stats = processor.process_video(
        video_path=video_path,
        output_path=output_video,
        show_video=True,  # 显示处理过程
        save_video=True   # 保存处理结果
    )
    
    if stats:
        # 保存统计报告
        output_dir = video_path.parent / "video_reports"
        processor.save_video_statistics(stats, output_dir)
        
        print("\n" + "=" * 50)
        print("🎉 视频处理完成！")
        print("📁 输出文件:")
        print(f"  视频: {output_video}")
        print(f"  报告: {output_dir}")
        print("\n📊 处理统计:")
        print(f"  总帧数: {stats['total_frames']}")
        print(f"  总目标数: {stats['total_objects']}")
        print(f"  处理时间: {stats['processing_time']:.2f}秒")
        print(f"  平均FPS: {stats['avg_fps']:.1f}")
        print("\n各类别统计:")
        for class_name, count in stats['class_counts'].items():
            if count > 0:
                print(f"  {class_name}: {count}")


def process_custom_video():
    """处理自定义视频"""
    print("🎬 自定义视频处理")
    print("=" * 30)
    
    # 获取用户输入
    video_path = input("请输入视频文件路径: ").strip()
    
    if not video_path:
        print("❌ 未输入视频路径")
        return
    
    video_path = Path(video_path)
    if not video_path.exists():
        print(f"❌ 视频文件不存在: {video_path}")
        return
    
    # 配置参数
    weights_path = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    
    if not weights_path.exists():
        print(f"❌ 模型文件不存在: {weights_path}")
        return
    
    print(f"✅ 模型文件: {weights_path}")
    print(f"✅ 视频文件: {video_path}")
    
    # 创建视频处理器
    processor = VideoProcessor(
        weights=str(weights_path),
        device='',
        conf_thres=0.25,
        iou_thres=0.45
    )
    
    # 设置输出路径
    output_video = video_path.parent / f"{video_path.stem}_detected{video_path.suffix}"
    
    print(f"\n开始处理视频...")
    
    # 处理视频
    stats = processor.process_video(
        video_path=video_path,
        output_path=output_video,
        show_video=True,
        save_video=True
    )
    
    if stats:
        # 保存统计报告
        output_dir = video_path.parent / "video_reports"
        processor.save_video_statistics(stats, output_dir)
        
        print(f"\n🎉 处理完成！")
        print(f"📁 输出视频: {output_video}")
        print(f"📊 总目标数: {stats['total_objects']}")


def batch_process_videos():
    """批量处理视频"""
    print("🎬 批量视频处理")
    print("=" * 30)
    
    # 获取视频目录
    video_dir = input("请输入视频目录路径: ").strip()
    
    if not video_dir:
        print("❌ 未输入目录路径")
        return
    
    video_dir = Path(video_dir)
    if not video_dir.exists():
        print(f"❌ 目录不存在: {video_dir}")
        return
    
    # 查找视频文件
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv']
    video_files = []
    for ext in video_extensions:
        video_files.extend(video_dir.glob(f"*{ext}"))
    
    if not video_files:
        print(f"❌ 在目录中未找到视频文件: {video_dir}")
        return
    
    print(f"✅ 找到 {len(video_files)} 个视频文件")
    
    # 配置参数
    weights_path = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    
    if not weights_path.exists():
        print(f"❌ 模型文件不存在: {weights_path}")
        return
    
    # 创建视频处理器
    processor = VideoProcessor(
        weights=str(weights_path),
        device='',
        conf_thres=0.25,
        iou_thres=0.45
    )
    
    # 处理每个视频
    total_stats = {
        'total_videos': len(video_files),
        'processed_videos': 0,
        'total_frames': 0,
        'total_objects': 0,
        'class_counts': {},
        'processing_time': 0
    }
    
    for i, video_file in enumerate(video_files, 1):
        print(f"\n📹 处理视频 {i}/{len(video_files)}: {video_file.name}")
        
        # 设置输出路径
        output_video = video_file.parent / f"{video_file.stem}_detected{video_file.suffix}"
        
        # 处理视频
        stats = processor.process_video(
            video_path=video_file,
            output_path=output_video,
            show_video=False,  # 批量处理时不显示
            save_video=True
        )
        
        if stats:
            total_stats['processed_videos'] += 1
            total_stats['total_frames'] += stats['total_frames']
            total_stats['total_objects'] += stats['total_objects']
            total_stats['processing_time'] += stats['processing_time']
            
            # 更新类别统计
            for class_name, count in stats['class_counts'].items():
                if class_name not in total_stats['class_counts']:
                    total_stats['class_counts'][class_name] = 0
                total_stats['class_counts'][class_name] += count
            
            print(f"  ✅ 完成: {stats['total_objects']} 个目标")
        else:
            print(f"  ❌ 处理失败")
    
    # 打印总统计
    print(f"\n" + "=" * 50)
    print("🎉 批量处理完成！")
    print("📊 总统计:")
    print(f"  处理视频数: {total_stats['processed_videos']}/{total_stats['total_videos']}")
    print(f"  总帧数: {total_stats['total_frames']}")
    print(f"  总目标数: {total_stats['total_objects']}")
    print(f"  总处理时间: {total_stats['processing_time']:.2f}秒")
    print("\n各类别统计:")
    for class_name, count in total_stats['class_counts'].items():
        if count > 0:
            print(f"  {class_name}: {count}")


if __name__ == "__main__":
    print("请选择视频处理模式:")
    print("1. 处理示例视频 (推荐)")
    print("2. 处理自定义视频")
    print("3. 批量处理视频")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            process_example_video()
        elif choice == "2":
            process_custom_video()
        elif choice == "3":
            batch_process_videos()
        else:
            print("❌ 无效选择，运行默认模式...")
            process_example_video()
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        print("请检查文件路径和依赖是否正确") 