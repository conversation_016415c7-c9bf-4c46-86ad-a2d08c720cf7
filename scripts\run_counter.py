#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv5目标计数运行脚本
简化版本，方便直接使用
"""

import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
project_dir = current_dir.parent
yolo_dir = project_dir / "相关源码和模型" / "VehicleCountor" / "yolov5"

# 检查路径是否存在
if not yolo_dir.exists():
    print(f"❌ YOLOv5目录不存在: {yolo_dir}")
    print("请检查路径是否正确")
    sys.exit(1)

# 添加YOLOv5路径到系统路径
if str(yolo_dir) not in sys.path:
    sys.path.append(str(yolo_dir))

# 导入目标计数类
from object_counter import ObjectCounter


def run_counting_example():
    """运行计数示例"""
    print("🚀 YOLOv5目标计数系统")
    print("=" * 50)
    
    # 配置参数
    weights_path = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    dataset_path = project_dir / "dataset_v1" / "images"
    
    # 检查文件是否存在
    if not weights_path.exists():
        print(f"❌ 模型文件不存在: {weights_path}")
        print("请先完成模型训练！")
        return
    
    if not dataset_path.exists():
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return
    
    print(f"✅ 模型文件: {weights_path}")
    print(f"✅ 数据集路径: {dataset_path}")
    print()
    
    # 创建计数器
    counter = ObjectCounter(
        weights=str(weights_path),
        device='',  # 自动选择设备
        conf_thres=0.25,
        iou_thres=0.45,
        max_det=1000
    )
    
    # 执行计数
    print("开始目标检测和计数...")
    print("-" * 30)
    
    # 测试不同数据集
    test_sets = [
        ("测试集", dataset_path / "test"),
        ("训练集", dataset_path / "train"),
        ("验证集", dataset_path / "val")
    ]
    
    for set_name, set_path in test_sets:
        if set_path.exists():
            print(f"\n📊 处理{set_name}: {set_path}")
            total_counts, frame_counts = counter.count_objects(
                source=str(set_path),
                imgsz=(1024, 1024),
                save_results=True,
                project=str(yolo_dir / "runs" / "count"),
                name=f"{set_name.lower()}_results"
            )
            
            # 打印结果
            print(f"  {set_name}统计结果:")
            for class_name, count in total_counts.items():
                if count > 0:
                    print(f"    {class_name}: {count}")
            print(f"  总目标数: {sum(total_counts.values())}")
            print(f"  处理图片数: {len(frame_counts)}")
    
    print("\n" + "=" * 50)
    print("🎉 所有数据集处理完成！")
    print("📁 结果保存在: runs/count/")
    print("📄 统计文件:")
    print("   - statistics.json (详细统计)")
    print("   - frame_statistics.csv (帧级统计)")
    print("   - summary.txt (摘要报告)")


def run_single_image():
    """处理单张图片"""
    print("🖼️ 单张图片目标计数")
    print("=" * 30)
    
    weights_path = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    test_image = project_dir / "dataset_v1" / "images" / "test" / "frame_00001.jpg"
    
    if not test_image.exists():
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    counter = ObjectCounter(
        weights=str(weights_path),
        device='',
        conf_thres=0.25,
        iou_thres=0.45
    )
    
    print(f"处理图片: {test_image}")
    total_counts, frame_counts = counter.count_objects(
        source=str(test_image),
        imgsz=(1024, 1024),
        save_results=True,
        project=str(yolo_dir / "runs" / "count"),
        name="single_image"
    )
    
    print("\n检测结果:")
    for class_name, count in total_counts.items():
        if count > 0:
            print(f"  {class_name}: {count}")


def run_video_processing():
    """处理视频文件"""
    print("🎬 视频目标计数")
    print("=" * 30)
    
    weights_path = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    video_path = yolo_dir / "example.mp4"
    
    if not video_path.exists():
        print(f"❌ 视频文件不存在: {video_path}")
        print("请将视频文件放在 yolov5/example.mp4")
        return
    
    counter = ObjectCounter(
        weights=str(weights_path),
        device='',
        conf_thres=0.25,
        iou_thres=0.45
    )
    
    print(f"处理视频: {video_path}")
    total_counts, frame_counts = counter.count_objects(
        source=str(video_path),
        imgsz=(1024, 1024),
        save_results=True,
        project=str(yolo_dir / "runs" / "count"),
        name="video_results"
    )
    
    print(f"\n视频统计结果:")
    print(f"总帧数: {len(frame_counts)}")
    for class_name, count in total_counts.items():
        if count > 0:
            print(f"  {class_name}: {count}")


if __name__ == "__main__":
    print("请选择运行模式:")
    print("1. 完整数据集计数 (推荐)")
    print("2. 单张图片计数")
    print("3. 视频处理")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            run_counting_example()
        elif choice == "2":
            run_single_image()
        elif choice == "3":
            run_video_processing()
        else:
            print("❌ 无效选择，运行默认模式...")
            run_counting_example()
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        print("请检查文件路径和依赖是否正确") 