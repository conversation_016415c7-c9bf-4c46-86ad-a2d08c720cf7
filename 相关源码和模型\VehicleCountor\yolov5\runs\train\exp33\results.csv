               epoch,      train/box_loss,      train/obj_loss,      train/cls_loss,   metrics/precision,      metrics/recall,     metrics/mAP_0.5,metrics/mAP_0.5:0.95,        val/box_loss,        val/obj_loss,        val/cls_loss,               x/lr0,               x/lr1,               x/lr2
                   0,             0.11398,            0.031697,                   0,                   0,                   0,                   0,                   0,             0.11658,            0.037714,                   0,              0.0001,              0.0001,              0.0991
                   1,              0.1184,            0.029962,                   0,                   0,                   0,                   0,                   0,             0.11631,            0.037872,                   0,           0.0002406,           0.0002406,            0.097241
                   2,             0.11336,            0.031777,                   0,                   0,                   0,                   0,                   0,             0.11592,            0.038067,                   0,            0.000302,            0.000302,            0.095302
                   3,             0.11222,            0.034739,                   0,                   0,                   0,                   0,                   0,             0.11541,            0.038366,                   0,           0.0002842,           0.0002842,            0.093284
                   4,             0.11325,            0.030747,                   0,                   0,                   0,                   0,                   0,             0.11504,            0.038569,                   0,           0.0001872,           0.0001872,            0.091187
