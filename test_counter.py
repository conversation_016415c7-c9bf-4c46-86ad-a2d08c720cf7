#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的目标计数测试脚本
直接调用YOLOv5的detect.py
"""

import os
import sys
import subprocess
from pathlib import Path

def run_detection_with_counting():
    """运行检测并统计结果"""
    print("🚀 YOLOv5目标检测与计数")
    print("=" * 40)
    
    # 获取当前目录
    current_dir = Path.cwd()
    yolo_dir = current_dir / "相关源码和模型" / "VehicleCountor" / "yolov5"
    
    if not yolo_dir.exists():
        print(f"❌ YOLOv5目录不存在: {yolo_dir}")
        return
    
    # 模型和数据集路径
    weights_path = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    dataset_path = current_dir / "dataset_v1" / "images"
    
    if not weights_path.exists():
        print(f"❌ 模型文件不存在: {weights_path}")
        return
    
    print(f"✅ 模型文件: {weights_path}")
    print(f"✅ 数据集路径: {dataset_path}")
    print()
    
    # 运行检测命令
    test_sets = [
        ("测试集", dataset_path / "test"),
        ("训练集", dataset_path / "train"),
        ("验证集", dataset_path / "val")
    ]
    
    for set_name, set_path in test_sets:
        if set_path.exists():
            print(f"\n📊 处理{set_name}: {set_path}")
            
            # 构建命令
            cmd = [
                "python", str(yolo_dir / "detect.py"),
                "--weights", str(weights_path),
                "--source", str(set_path),
                "--img", "1024",
                "--conf", "0.25",
                "--project", str(yolo_dir / "runs" / "count"),
                "--name", f"{set_name.lower()}_count"
            ]
            
            print(f"执行命令: {' '.join(cmd)}")
            
            try:
                # 切换到yolo目录执行
                result = subprocess.run(
                    cmd,
                    cwd=str(yolo_dir),
                    capture_output=True,
                    text=True,
                    encoding='utf-8'
                )
                
                if result.returncode == 0:
                    print(f"✅ {set_name}处理完成")
                    # 解析输出中的计数信息
                    for line in result.stdout.split('\n'):
                        if 'tricycles' in line or 'cars' in line or 'persons' in line:
                            print(f"  {line.strip()}")
                else:
                    print(f"❌ {set_name}处理失败")
                    print(f"错误信息: {result.stderr}")
                    
            except Exception as e:
                print(f"❌ 执行出错: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 检测完成！")
    print("📁 结果保存在: runs/count/")
    print("📄 查看生成的图片文件获取详细计数信息")


if __name__ == "__main__":
    run_detection_with_counting() 