{"time":"2025-07-20T19:56:47.8025686+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-20T19:56:49.5594025+08:00","level":"INFO","msg":"stream: created new stream","id":"en2hpcvr"}
{"time":"2025-07-20T19:56:49.5594025+08:00","level":"INFO","msg":"stream: started","id":"en2hpcvr"}
{"time":"2025-07-20T19:56:49.5594025+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"en2hpcvr"}
{"time":"2025-07-20T19:56:49.5594025+08:00","level":"INFO","msg":"handler: started","stream_id":"en2hpcvr"}
{"time":"2025-07-20T19:56:49.5594025+08:00","level":"INFO","msg":"sender: started","stream_id":"en2hpcvr"}
{"time":"2025-07-20T19:57:05.1542025+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:20.1541967+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:35.1544239+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:57:50.1539744+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:05.1543219+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:20.1541491+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:35.1546498+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:58:50.1540006+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:05.1545297+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:20.1542382+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:35.1544906+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T19:59:50.1540105+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:05.1539884+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:20.1541123+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:35.1544568+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:00:50.1574941+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:05.1545119+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:20.1545055+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:35.1540515+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:01:50.1542879+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:05.1544543+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:20.1540635+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:35.1544074+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:02:50.1546298+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:05.1555329+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:20.1544133+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:35.154058+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:03:50.1543621+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:04:05.1543168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:04:20.154218+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:04:35.1543836+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:04:50.1539536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:05.1543898+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:20.1543658+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:35.1544466+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:05:50.1541986+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:05.1539854+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:20.1547864+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:35.1542386+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:06:50.1541216+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:05.1547371+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:20.1648203+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:35.1557341+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:07:50.1552144+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:08:05.1545491+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:08:20.1539755+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:08:35.1542496+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:08:50.1541289+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:09:05.1541868+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:09:20.1545202+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:09:35.1542379+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:09:50.1544179+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:10:05.1545508+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:10:20.1546324+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-20T20:10:35.1543997+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
