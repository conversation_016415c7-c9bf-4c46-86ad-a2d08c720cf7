#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新Web界面使用YOLOv5x模型
"""

import os
import shutil
from pathlib import Path

def copy_yolov5x_weights():
    """复制YOLOv5x权重到训练目录"""
    print("📁 复制YOLOv5x权重...")
    
    # 源文件路径
    source_weights = Path("../../../models/yolov5x.pt")
    
    # 目标路径 - 复制到exp21目录
    target_dir = Path("runs/train/exp21/weights")
    target_weights = target_dir / "yolov5x.pt"
    
    if not source_weights.exists():
        print(f"❌ YOLOv5x权重文件不存在: {source_weights}")
        return False
    
    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制文件
    shutil.copy2(source_weights, target_weights)
    print(f"✅ YOLOv5x权重已复制到: {target_weights}")
    return True

def update_web_interface():
    """更新Web界面配置"""
    print("\n🌐 更新Web界面配置...")
    
    web_interface_path = Path("../../../scripts/web_interface.py")
    
    if not web_interface_path.exists():
        print(f"❌ Web界面文件不存在: {web_interface_path}")
        return False
    
    # 读取Web界面文件
    with open(web_interface_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 新的模型路径 - 使用YOLOv5x
    new_model_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\runs\train\exp21\weights\yolov5x.pt"
    
    # 替换模型路径
    old_model_path = r"D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\runs\train\exp21\weights\last.pt"
    content = content.replace(old_model_path, new_model_path)
    
    # 更新模型名称和描述
    content = content.replace("YOLOv5n", "YOLOv5x")
    content = content.replace("YOLOv5 Web界面系统", "YOLOv5x 高精度Web界面系统")
    content = content.replace("YOLOv5n summary", "YOLOv5x summary")
    
    # 写回文件
    with open(web_interface_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Web界面已更新为YOLOv5x!")
    return True

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建YOLOv5x测试脚本...")
    
    test_script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv5x 车辆检测测试脚本
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

def run_inference():
    """运行推理"""
    # 训练好的模型路径
    weights = "runs/train/exp21/weights/yolov5x.pt"
    
    # 测试图片
    source = "../../../dataset_v2/yolo_format/val/images"
    
    # 推理参数
    conf_thres = 0.25
    iou_thres = 0.45
    
    print("🔍 YOLOv5x 车辆检测推理")
    print("=" * 40)
    print(f"🎯 模型: {weights}")
    print(f"📸 测试图片: {source}")
    print(f"📊 置信度阈值: {conf_thres}")
    print(f"🔗 IoU阈值: {iou_thres}")
    
    # 构建推理命令
    cmd = f"""python detect.py --weights {weights} --source "{source}" --conf {conf_thres} --iou {iou_thres} --project runs/detect --name yolov5x_test --exist-ok --save-txt --save-conf"""
    
    # 切换到YOLOv5目录
    os.chdir(yolo_dir)
    
    # 执行推理
    os.system(cmd)
    print("✅ 推理完成!")

if __name__ == "__main__":
    run_inference()
'''
    
    script_path = Path("../../../scripts/test_yolov5x.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(test_script_content)
    
    print(f"✅ 测试脚本已创建: {script_path}")

def main():
    """主函数"""
    print("🎯 YOLOv5x 模型更新工具")
    print("=" * 50)
    
    # 复制YOLOv5x权重
    if copy_yolov5x_weights():
        # 更新Web界面
        if update_web_interface():
            # 创建测试脚本
            create_test_script()
            
            print("\n" + "=" * 50)
            print("🎉 YOLOv5x模型更新完成!")
            print("\n📋 下一步操作:")
            print("1. 启动Web界面: python ../../../scripts/web_interface.py")
            print("2. 测试模型: python ../../../scripts/test_yolov5x.py")
            print("3. 访问地址: http://localhost:5000")
            
            print("\n💡 YOLOv5x优势:")
            print("- 更高的检测精度")
            print("- 更好的小目标检测能力")
            print("- 更强的特征提取能力")
            print("- 适合复杂场景")
        else:
            print("\n❌ Web界面更新失败")
    else:
        print("\n❌ YOLOv5x权重复制失败")

if __name__ == "__main__":
    main() 