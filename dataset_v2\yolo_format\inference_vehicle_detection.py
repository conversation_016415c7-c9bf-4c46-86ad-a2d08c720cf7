#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆检测推理脚本 - 数据集V2
"""

import os
import sys
from pathlib import Path

# 添加YOLOv5路径
yolo_dir = Path(__file__).parent.parent / "相关源码和模型" / "VehicleCountor" / "yolov5"
sys.path.insert(0, str(yolo_dir))

from detect import run

if __name__ == "__main__":
    # 推理参数
    weights = "runs/train/vehicle_detection_v2/weights/best.pt"  # 训练好的模型
    source = "test_images"  # 测试图片目录
    conf_thres = 0.25
    iou_thres = 0.45
    
    print("🔍 开始车辆检测推理...")
    print(f"🎯 模型权重: {weights}")
    print(f"📸 测试图片: {source}")
    print(f"📊 置信度阈值: {conf_thres}")
    print(f"🔗 IoU阈值: {iou_thres}")
    print("-" * 50)
    
    # 开始推理
    run(
        weights=weights,
        source=source,
        conf_thres=conf_thres,
        iou_thres=iou_thres,
        project="runs/detect",
        name="vehicle_detection_v2",
        exist_ok=True,
        save_txt=True,
        save_conf=True
    )
    
    print("✅ 推理完成!")
