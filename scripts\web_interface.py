#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面
基于Flask的交通目标检测与计数Web应用
"""

import os
import sys
import cv2
import numpy as np
import json
import base64
from pathlib import Path
from datetime import datetime
import time
import threading
from flask import Flask, render_template, request, jsonify, send_file, Response
from werkzeug.utils import secure_filename
import io

# 设置环境变量
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["VECLIB_MAXIMUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"

# 添加项目路径
current_dir = Path(__file__).parent
project_dir = current_dir.parent
yolo_dir = project_dir / "相关源码和模型" / "VehicleCountor" / "yolov5"
vehicle_dir = project_dir / "相关源码和模型" / "VehicleCountor"

# 添加路径到系统路径
sys.path.insert(0, str(yolo_dir))
sys.path.insert(0, str(vehicle_dir))

from yolov5.models.common import DetectMultiBackend
from yolov5.utils.general import (LOGGER, check_img_size, non_max_suppression, scale_coords,
                                  check_imshow, xyxy2xywh, increment_path, strip_optimizer, colorstr)
from yolov5.utils.torch_utils import select_device, time_sync
from yolov5.utils.plots import Annotator, colors, save_one_box


class WebProcessor:
    def __init__(self, yolo_weights, device='', conf_thres=0.25, iou_thres=0.45, max_det=1000):
        """初始化Web处理器"""
        self.device = select_device(device)
        
        # 加载YOLOv5模型
        self.model = DetectMultiBackend(yolo_weights, device=self.device)
        self.stride, self.names, self.pt = self.model.stride, self.model.names, self.model.pt
        
        self.conf_thres = conf_thres
        self.iou_thres = iou_thres
        self.max_det = max_det
        
        # 统计信息
        self.frame_count = 0
        self.total_objects = 0
        self.class_counts = {name: 0 for name in self.names}
        self.track_history = {}  # 轨迹历史
        self.frame_statistics = []
        self.next_track_id = 1
        
        # 模型预热
        self.model.warmup(imgsz=(1, 3, 640, 640))
        
        print(f"✅ YOLOv5模型加载完成: {yolo_weights}")
        print(f"✅ 设备: {self.device}")
        print(f"✅ 类别: {self.names}")
        
    def process_image(self, image_data):
        """处理单张图片"""
        # 解码图片
        if isinstance(image_data, str):
            # Base64编码的图片
            image_data = base64.b64decode(image_data.split(',')[1])
        
        # 转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # 处理图片
        processed_frame, frame_stats = self.process_frame(frame)
        
        # 编码处理后的图片
        _, buffer = cv2.imencode('.jpg', processed_frame)
        processed_image = base64.b64encode(buffer).decode('utf-8')
        
        return {
            'processed_image': f"data:image/jpeg;base64,{processed_image}",
            'statistics': frame_stats,
            'class_counts': self.class_counts
        }
    
    def process_frame(self, frame):
        """处理单帧"""
        # 预处理
        im = cv2.resize(frame, (640, 640))
        im = im.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
        im = np.ascontiguousarray(im)
        im = np.expand_dims(im, axis=0)
        
        # 转换为tensor
        import torch
        im = torch.from_numpy(im).to(self.device)
        im = im.half() if self.model.fp16 else im.float()
        im /= 255.0
        
        # YOLOv5推理
        pred = self.model(im, augment=False, visualize=False)
        
        # NMS
        pred = non_max_suppression(pred, self.conf_thres, self.iou_thres, 
                                 classes=None, max_det=self.max_det)
        
        # 处理检测结果
        frame_stats = {
            'objects_detected': 0,
            'tracks_active': 0,
            'class_detections': {name: 0 for name in self.names}
        }
        
        annotator = Annotator(frame, line_width=3, example=str(self.names))
        
        for det in pred:
            if len(det):
                # 缩放边界框到原始尺寸
                det[:, :4] = scale_coords(im.shape[2:], det[:, :4], frame.shape).round()
                
                # 简化跟踪：为每个检测分配一个ID
                for i, (*xyxy, conf, cls) in enumerate(det):
                    x1, y1, x2, y2 = map(int, xyxy)
                    class_id = int(cls)
                    class_name = self.names[class_id]
                    
                    # 生成简单的track_id
                    track_id = self.next_track_id
                    self.next_track_id += 1
                    
                    # 更新轨迹历史
                    if track_id not in self.track_history:
                        self.track_history[track_id] = []
                    
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    self.track_history[track_id].append({
                        'frame': self.frame_count,
                        'x': center_x,
                        'y': center_y,
                        'class': class_id,
                        'bbox': [x1, y1, x2, y2]
                    })
                    
                    # 统计检测结果
                    frame_stats['class_detections'][class_name] += 1
                    frame_stats['objects_detected'] += 1
                    self.class_counts[class_name] += 1
                    self.total_objects += 1
                    
                    # 绘制边界框和轨迹
                    label = f'{class_name} {track_id}'
                    color = colors(track_id % 20, True)  # 使用track_id作为颜色
                    annotator.box_label([x1, y1, x2, y2], label, color=color)
                    
                    # 绘制轨迹
                    if len(self.track_history[track_id]) > 1:
                        points = []
                        for point in self.track_history[track_id][-10:]:  # 最近10个点
                            points.append((point['x'], point['y']))
                        
                        if len(points) > 1:
                            points = np.array(points, dtype=np.int32)
                            cv2.polylines(frame, [points], False, color, 2)
        
        frame_stats['tracks_active'] = frame_stats['objects_detected']
        
        # 在帧上显示统计信息
        self.draw_statistics(frame, frame_stats)
        
        return frame, frame_stats
    
    def draw_statistics(self, frame, frame_stats):
        """在帧上绘制统计信息"""
        # 绘制当前帧统计
        y_offset = 30
        cv2.putText(frame, f"Objects: {frame_stats['objects_detected']}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        y_offset += 20
        
        cv2.putText(frame, f"Tracks: {frame_stats['tracks_active']}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        y_offset += 20
        
        # 绘制类别统计
        for class_name, count in frame_stats['class_detections'].items():
            if count > 0:
                cv2.putText(frame, f"{class_name}: {count}", (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                y_offset += 18
        
        # 绘制总统计
        y_offset += 10
        cv2.putText(frame, f"Total: {self.total_objects}", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        y_offset += 25
        
        for class_name, count in self.class_counts.items():
            if count > 0:
                cv2.putText(frame, f"{class_name}: {count}", (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                y_offset += 18


# 创建Flask应用
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size

# 全局处理器
processor = None

# 上传文件夹
UPLOAD_FOLDER = project_dir / 'uploads'
UPLOAD_FOLDER.mkdir(exist_ok=True)
app.config['UPLOAD_FOLDER'] = str(UPLOAD_FOLDER)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    if 'file' not in request.files:
        return jsonify({'error': '没有文件被上传'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = Path(app.config['UPLOAD_FOLDER']) / filename
        file.save(str(filepath))
        
        # 根据文件类型处理
        if filepath.suffix.lower() in ['.mp4', '.avi', '.mov']:
            # 视频文件
            return jsonify({
                'type': 'video',
                'filename': filename,
                'message': '视频文件上传成功，开始处理...'
            })
        else:
            # 图片文件
            try:
                # 读取图片
                with open(filepath, 'rb') as f:
                    image_data = f.read()
                
                # 处理图片
                result = processor.process_image(image_data)
                result['type'] = 'image'
                result['filename'] = filename
                
                return jsonify(result)
            except Exception as e:
                return jsonify({'error': f'处理图片时出错: {str(e)}'}), 500
    
    return jsonify({'error': '不支持的文件类型'}), 400

@app.route('/process_video', methods=['POST'])
def process_video():
    """处理视频文件"""
    data = request.get_json()
    filename = data.get('filename')
    
    if not filename:
        return jsonify({'error': '没有提供文件名'}), 400
    
    filepath = Path(app.config['UPLOAD_FOLDER']) / filename
    
    if not filepath.exists():
        return jsonify({'error': '文件不存在'}), 404
    
    try:
        # 处理视频
        result = process_video_file(filepath)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'处理视频时出错: {str(e)}'}), 500

def process_video_file(video_path):
    """处理视频文件"""
    # 打开视频文件
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        raise Exception("无法打开视频文件")
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 设置输出视频
    output_path = video_path.parent / f"{video_path.stem}_processed{video_path.suffix}"
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    video_writer = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
    
    # 处理视频
    frame_count = 0
    total_objects = 0
    class_counts = {name: 0 for name in processor.names}
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 处理当前帧
        processed_frame, frame_stats = processor.process_frame(frame)
        
        # 保存视频
        video_writer.write(processed_frame)
        
        # 更新统计
        total_objects += frame_stats['objects_detected']
        for class_name, count in frame_stats['class_detections'].items():
            class_counts[class_name] += count
    
    # 清理资源
    cap.release()
    video_writer.release()
    
    return {
        'type': 'video',
        'filename': video_path.name,
        'processed_filename': output_path.name,
        'statistics': {
            'total_frames': frame_count,
            'total_objects': total_objects,
            'class_counts': class_counts,
            'duration': frame_count / fps if fps > 0 else 0
        }
    }

@app.route('/download/<filename>')
def download_file(filename):
    """下载处理后的文件"""
    filepath = Path(app.config['UPLOAD_FOLDER']) / filename
    if filepath.exists():
        return send_file(str(filepath), as_attachment=True)
    return jsonify({'error': '文件不存在'}), 404

@app.route('/api/statistics')
def get_statistics():
    """获取统计信息"""
    if processor:
        return jsonify({
            'total_objects': processor.total_objects,
            'class_counts': processor.class_counts,
            'frame_count': processor.frame_count
        })
    return jsonify({'error': '处理器未初始化'}), 500

@app.route('/api/reset')
def reset_statistics():
    """重置统计信息"""
    if processor:
        processor.frame_count = 0
        processor.total_objects = 0
        processor.class_counts = {name: 0 for name in processor.names}
        processor.track_history = {}
        processor.frame_statistics = []
        processor.next_track_id = 1
        return jsonify({'message': '统计信息已重置'})
    return jsonify({'error': '处理器未初始化'}), 500


# 创建HTML模板
def create_templates():
    """创建HTML模板"""
    templates_dir = current_dir / 'templates'
    templates_dir.mkdir(exist_ok=True)
    
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交通目标检测与计数系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .upload-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .upload-section.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .upload-btn:hover {
            transform: translateY(-2px);
        }
        .result-section {
            display: none;
            margin-top: 30px;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .processed-image {
            max-width: 100%;
            max-height: 500px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .statistics {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .statistics h3 {
            margin-top: 0;
            color: #333;
        }
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            margin: 0 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 交通目标检测与计数系统</h1>
            <p>基于YOLOv5的智能交通监控系统</p>
        </div>
        
        <div class="content">
            <div class="upload-section" id="uploadSection">
                <h2>📁 上传文件</h2>
                <p>支持图片格式：PNG, JPG, JPEG, GIF</p>
                <p>支持视频格式：MP4, AVI, MOV</p>
                <br>
                <input type="file" id="fileInput" class="file-input" accept="image/*,video/*">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    📤 选择文件
                </button>
                <p id="dragText">或拖拽文件到此处</p>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在处理中，请稍候...</p>
            </div>
            
            <div class="result-section" id="resultSection">
                <h2>📊 处理结果</h2>
                <div class="image-container" id="imageContainer"></div>
                <div class="statistics" id="statistics"></div>
                <div class="controls" id="controls"></div>
            </div>
        </div>
    </div>

    <script>
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');
        const loading = document.getElementById('loading');
        const resultSection = document.getElementById('resultSection');
        const imageContainer = document.getElementById('imageContainer');
        const statistics = document.getElementById('statistics');
        const controls = document.getElementById('controls');

        // 拖拽上传
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            loading.style.display = 'block';
            resultSection.style.display = 'none';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.error) {
                    showError(data.error);
                    return;
                }

                if (data.type === 'image') {
                    showImageResult(data);
                } else if (data.type === 'video') {
                    showVideoResult(data);
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                showError('处理文件时出错: ' + error.message);
            });
        }

        function showImageResult(data) {
            imageContainer.innerHTML = `
                <img src="${data.processed_image}" alt="处理结果" class="processed-image">
            `;
            
            showStatistics(data.statistics, data.class_counts);
            showControls(data.filename);
            resultSection.style.display = 'block';
        }

        function showVideoResult(data) {
            // 处理视频
            fetch('/process_video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({filename: data.filename})
            })
            .then(response => response.json())
            .then(result => {
                if (result.error) {
                    showError(result.error);
                    return;
                }
                
                imageContainer.innerHTML = `
                    <h3>🎬 视频处理完成</h3>
                    <p>原始文件: ${result.filename}</p>
                    <p>处理后文件: ${result.processed_filename}</p>
                `;
                
                showStatistics(result.statistics, result.statistics.class_counts);
                showControls(result.processed_filename, true);
                resultSection.style.display = 'block';
            })
            .catch(error => {
                showError('处理视频时出错: ' + error.message);
            });
        }

        function showStatistics(frameStats, classCounts) {
            let html = '<h3>📈 检测统计</h3>';
            
            if (frameStats) {
                html += '<div class="stat-grid">';
                html += `<div class="stat-item">
                    <div class="stat-value">${frameStats.objects_detected || 0}</div>
                    <div class="stat-label">当前帧目标数</div>
                </div>`;
                html += `<div class="stat-item">
                    <div class="stat-value">${frameStats.tracks_active || 0}</div>
                    <div class="stat-label">活跃轨迹数</div>
                </div>`;
                html += '</div>';
            }
            
            if (classCounts) {
                html += '<h4>各类别统计:</h4>';
                html += '<div class="stat-grid">';
                for (const [className, count] of Object.entries(classCounts)) {
                    if (count > 0) {
                        html += `<div class="stat-item">
                            <div class="stat-value">${count}</div>
                            <div class="stat-label">${className}</div>
                        </div>`;
                    }
                }
                html += '</div>';
            }
            
            statistics.innerHTML = html;
        }

        function showControls(filename, isVideo = false) {
            let html = '';
            if (isVideo) {
                html += `<a href="/download/${filename}" class="btn">📥 下载处理后的视频</a>`;
            }
            html += `<button class="btn" onclick="resetStats()">🔄 重置统计</button>`;
            controls.innerHTML = html;
        }

        function showError(message) {
            resultSection.style.display = 'block';
            resultSection.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        function resetStats() {
            fetch('/api/reset')
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert('统计信息已重置');
                    location.reload();
                }
            })
            .catch(error => {
                alert('重置统计时出错: ' + error.message);
            });
        }
    </script>
</body>
</html>
    """
    
    with open(templates_dir / 'index.html', 'w', encoding='utf-8') as f:
        f.write(html_content)


def main():
    """主函数"""
    print("🌐 YOLOv5x 高精度Web界面系统")
    print("=" * 60)
    
    # 配置参数
    yolo_weights = yolo_dir / "runs" / "train" / "exp21" / "weights" / "last.pt"
    
    # 检查文件是否存在
    if not yolo_weights.exists():
        print(f"❌ YOLOv5模型文件不存在: {yolo_weights}")
        return
    
    print(f"✅ YOLOv5模型: {yolo_weights}")
    print()
    
    # 创建全局处理器
    global processor
    processor = WebProcessor(
        yolo_weights=str(yolo_weights),
        device='',  # 自动选择设备
        conf_thres=0.25,
        iou_thres=0.45,
        max_det=1000
    )
    
    # 创建HTML模板
    create_templates()
    
    print("🚀 启动Web服务器...")
    print("📱 访问地址: http://localhost:5000")
    print("💡 按 Ctrl+C 停止服务器")
    print("-" * 40)
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5000, debug=False)


if __name__ == "__main__":
    main() 