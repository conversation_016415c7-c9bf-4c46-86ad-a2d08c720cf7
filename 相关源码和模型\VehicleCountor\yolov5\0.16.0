Collecting supervision
  Using cached supervision-0.25.1-py3-none-any.whl (181 kB)
Requirement already satisfied: opencv-python>=4.5.5.64 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (4.6.0)
Collecting defusedxml<0.8.0,>=0.7.1
  Using cached defusedxml-0.7.1-py2.py3-none-any.whl (25 kB)
Requirement already satisfied: numpy>=1.21.2 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (1.24.4)
Requirement already satisfied: matplotlib<3.8.0,>=3.6.0 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (3.7.5)
Requirement already satisfied: contourpy>=1.0.7 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (1.1.1)
Requirement already satisfied: pillow>=9.4 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (10.4.0)
Requirement already satisfied: requests>=2.26.0 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (2.32.3)
Requirement already satisfied: tqdm>=4.62.3 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (4.66.5)
Requirement already satisfied: scipy==1.10.0 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (1.10.0)
Requirement already satisfied: pyyaml>=5.3 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from supervision) (6.0.2)
Requirement already satisfied: importlib-resources>=3.2.0 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from matplotlib<3.8.0,>=3.6.0->supervision) (6.4.5)
Requirement already satisfied: kiwisolver>=1.0.1 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from matplotlib<3.8.0,>=3.6.0->supervision) (1.4.5)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from matplotlib<3.8.0,>=3.6.0->supervision) (0.12.1)
Requirement already satisfied: pyparsing>=2.3.1 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from matplotlib<3.8.0,>=3.6.0->supervision) (3.1.4)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from matplotlib<3.8.0,>=3.6.0->supervision) (25.0)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from matplotlib<3.8.0,>=3.6.0->supervision) (4.53.1)
Requirement already satisfied: python-dateutil>=2.7 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from matplotlib<3.8.0,>=3.6.0->supervision) (2.9.0)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from requests>=2.26.0->supervision) (3.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from requests>=2.26.0->supervision) (2025.4.26)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from requests>=2.26.0->supervision) (2.2.3)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from requests>=2.26.0->supervision) (3.10)
Requirement already satisfied: colorama in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from tqdm>=4.62.3->supervision) (0.4.6)
Requirement already satisfied: zipp>=3.1.0 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from importlib-resources>=3.2.0->matplotlib<3.8.0,>=3.6.0->supervision) (3.20.2)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\.conda\envs\yoloobb\lib\site-packages (from python-dateutil>=2.7->matplotlib<3.8.0,>=3.6.0->supervision) (1.16.0)
Installing collected packages: defusedxml, supervision
Successfully installed defusedxml-0.7.1 supervision-0.25.1
