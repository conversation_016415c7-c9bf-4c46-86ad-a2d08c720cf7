
Dataset not found , missing paths ['D:\\\\\\YOLO-OBB\\lkr_yolo\\traffic_counter_project\\dataset_v1\\images\\val']
Traceback (most recent call last):
  File "train.py", line 667, in <module>
    main(opt)
  File "train.py", line 562, in main
    train(opt.hyp, opt, device, callbacks)
  File "train.py", line 92, in train
    loggers = Loggers(save_dir, weights, opt, hyp, LOGGER)  # loggers instance
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\utils\loggers\__init__.py", line 83, in __init__
    self.wandb = WandbLogger(self.opt, run_id)
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\utils\loggers\wandb\wandb_utils.py", line 180, in __init__
    self.data_dict = check_wandb_dataset(opt.data)
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\utils\loggers\wandb\wandb_utils.py", line 56, in check_wandb_dataset
    return check_dataset(data_file)
  File "D:\大三下\智能信息处理技术\基于YOLO-OBB算法的交通智能体目标识别算法\lkr_yolo\traffic_counter_project\相关源码和模型\VehicleCountor\yolov5\utils\general.py", line 489, in check_dataset
    raise Exception(emojis('Dataset not found ❌'))
Exception: Dataset not found
