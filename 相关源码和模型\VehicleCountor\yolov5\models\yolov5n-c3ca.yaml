# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

# Parameters
nc: 2  # number of classes, will be overridden by dataset.yaml
depth_multiple: 0.33  # model depth multiple
width_multiple: 0.25  # layer channel multiple
anchors:
  - [10,13, 16,30, 33,23]  # P3/8
  - [30,61, 62,45, 59,119]  # P4/16
  - [116,90, 156,198, 373,326]  # P5/32

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3, [1024]],
   [-1, 1, <PERSON><PERSON><PERSON>, [1024, 5]],  # 9
  ]

# YOLOv5 v6.0 head with C3<PERSON>
head:
  [[-1, 1, Conv, [512, 1, 1]],           # 10
   [-1, 1, nn.Upsample, [None, 2, 'nearest']], # 11
   [[-1, 6], 1, Concat, [1]],             # 12 cat backbone P4
   [-1, 3, C3CA, [512, False]],           # 13

   [-1, 1, Conv, [256, 1, 1]],           # 14
   [-1, 1, nn.Upsample, [None, 2, 'nearest']], # 15
   [[-1, 4], 1, Concat, [1]],             # 16 cat backbone P3
   [-1, 3, C3CA, [256, False]],           # 17 (P3/8-small)

   [-1, 1, Conv, [256, 3, 2]],           # 18
   [[-1, 14], 1, Concat, [1]],            # 19 cat head P4
   [-1, 3, C3CA, [512, False]],           # 20 (P4/16-medium)

   [-1, 1, Conv, [512, 3, 2]],           # 21
   [[-1, 10], 1, Concat, [1]],            # 22 cat head P5
   [-1, 3, C3CA, [1024, False]],          # 23 (P5/32-large)

   [[17, 20, 23], 1, Detect, [nc, anchors]],  # Detect(P3, P4, P5)
  ]
